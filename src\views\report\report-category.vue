<template>
  <div class="app-container">
    <!-- 查詢條件 -->
    <el-card>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <!-- 分類類型選擇 -->
        <el-form-item label="Type Report">
          <el-select
            v-model="selectedCategoryType"
            placeholder="選擇分類類型"
            style="width: 200px;"
          >
            <el-option label="Shopping 分類" value="shop_category" />
            <el-option label="Dining 分類" value="food_category" />
          </el-select>
        </el-form-item>

        <!-- 年份選擇 -->
        <el-form-item label="Year Report">
          <el-select
            v-model="selectedYear"
            placeholder="選擇年份"
            style="width: 120px;"
          >
            <el-option label="All" value="" />
            <el-option
              v-for="year in availableYears"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
        </el-form-item>

        <!-- 月份選擇 -->
        <el-form-item label="Month">
          <el-select
            v-model="selectedMonth"
            placeholder="選擇月份"
            style="width: 120px;"
          >
            <el-option label="All" value="" />
            <el-option
              v-for="month in availableMonths"
              :key="month.value"
              :label="month.label"
              :value="month.value"
            />
          </el-select>
        </el-form-item>

        <!-- 設備選擇 -->
        <el-form-item label="Date">
          <el-select
            v-model="selectedDevice"
            placeholder="選擇設備（默認全部）"
            clearable
            filterable
            style="width: 200px;"
          >
            <el-option label="全部設備" value="" />
            <el-option
              v-for="device in devices"
              :key="device.machineId"
              :label="`${device.name} (${device.mall} ${device.floor})`"
              :value="device.machineId"
            />
          </el-select>
        </el-form-item>

        <!-- 操作按鈕 -->
        <el-form-item>
          <el-button type="primary" @click="generateTrendChart" :loading="chartLoading">
            <i class="el-icon-search"></i> 生成分類訪問排行榜
          </el-button>
          <el-button @click="resetSearch">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 統計摘要 -->
    <div v-if="chartData.length > 0" class="summary-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">總訪問次數</div>
            <div class="summary-value">{{ totalVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">分類總數</div>
            <div class="summary-value">{{ totalCategories.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">最熱門分類訪問量</div>
            <div class="summary-value">{{ maxCategoryVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">平均每分類訪問量</div>
            <div class="summary-value">{{ averageCategoryVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 圖表展示 -->
    <div v-if="chartData.length > 0" class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>{{ getCategoryTypeLabel() }}訪問排行榜</span>
          <div class="chart-actions">
            <el-dropdown @command="handleExport">
              <el-button type="text">
                <i class="el-icon-download"></i> 導出
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">PNG圖片</el-dropdown-item>
                <el-dropdown-item command="jpeg">JPEG圖片</el-dropdown-item>
                
                <el-dropdown-item command="xlsx">Excel文件</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="chart-wrapper">
          <div v-loading="chartLoading" ref="trendChart" class="trend-chart"></div>
          <div v-if="!chartLoading && chartData.length === 0" class="no-data">
            <i class="el-icon-info"></i>
            <p>暫無數據，請選擇時間範圍後點擊查詢</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 數據表格 -->
    <div v-if="tableData.length > 0" class="data-container">
      <el-card>
        <div slot="header">
          <span>詳細數據</span>
        </div>
        <el-table v-loading="chartLoading" :data="tableData" border stripe max-height="400">
          <el-table-column type="index" label="排名" width="80" fixed="left">
            <template slot-scope="scope">
              <span style="font-weight: bold; color: #409EFF;">{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="categoryName" label="分類名稱" min-width="200" show-overflow-tooltip />
          <el-table-column prop="visitCount" label="訪問次數" width="120" sortable>
            <template slot-scope="scope">
              <span style="font-weight: bold; color: #409EFF;">{{ scope.row.visitCount.toLocaleString() }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'ReportCategory',
  data() {
    return {
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: 'statistical', // API名稱

      form: {},

      // 查詢條件
      selectedCategoryType: 'food_category', // 默認選擇 Dining 分類
      selectedYear: '', // 選中的年份，空字符串表示全部年份
      selectedMonth: '', // 選中的月份，空字符串表示全部月份
      selectedDevice: '', // 選中的設備ID，空字符串表示全部設備
      devices: [], // 設備列表

      // 年份和月份選項
      availableYears: ['2025', '2026', '2027', '2028', '2029', '2030', '2031'],
      availableMonths: [
        { label: '1', value: '01' },
        { label: '2', value: '02' },
        { label: '3', value: '03' },
        { label: '4', value: '04' },
        { label: '5', value: '05' },
        { label: '6', value: '06' },
        { label: '7', value: '07' },
        { label: '8', value: '08' },
        { label: '9', value: '09' },
        { label: '10', value: '10' },
        { label: '11', value: '11' },
        { label: '12', value: '12' }
      ],

      // 查詢控制
      maxRetries: 3, // 最大重試次數
      retryDelay: 2000, // 重試延遲（毫秒）

      // 圖表相關
      chart: null, // echarts實例
      chartData: [], // 圖表數據
      tableData: [], // 表格數據
      chartLoading: false // 圖表加載狀態
    }
  },
  computed: {
    // 計算統計摘要
    totalVisits() {
      if (this.chartData.length === 0) return 0
      return this.chartData.reduce((sum, item) => sum + (item.visitCount || 0), 0)
    },

    totalCategories() {
      return this.chartData.length
    },

    maxCategoryVisits() {
      if (this.chartData.length === 0) return 0
      return Math.max(...this.chartData.map(item => item.visitCount || 0))
    },

    averageCategoryVisits() {
      if (this.chartData.length === 0) return 0
      return Math.round(this.totalVisits / this.chartData.length)
    }
  },
  created() {
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl

    // 獲取設備列表
    this.getDevices()
  },
  mounted() {
    // 監聽來自 iframe 的消息
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    // 移除消息監聽
    window.removeEventListener('message', this.handleMessage)
    // 銷毀圖表實例
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 處理消息
    handleMessage(event) {
      // 處理來自iframe的消息
    },

    // 獲取分類類型標籤
    getCategoryTypeLabel() {
      return this.selectedCategoryType === 'food_category' ? 'Dining 分類' : 'Shopping 分類'
    },

    // 獲取設備列表
    async getDevices() {
      try {
        const response = await fetch('https://yohomall.esc-map.com/api/machines')
        const data = await response.json()
        this.devices = data.data || []
        console.log('獲取到設備列表:', this.devices.length, '個設備')
      } catch (error) {
        console.error('獲取設備列表失敗:', error)
        this.$message.error('獲取設備列表失敗')
        this.devices = []
      }
    },

    // 獲取時間範圍
    getTimeRange() {
      let startDate, endDate

      if (this.selectedYear && this.selectedMonth) {
        // 指定年月
        startDate = `${this.selectedYear}-${this.selectedMonth}-01T00:00:00.000Z`
        let nextMonth = parseInt(this.selectedMonth) + 1
        let nextYear = parseInt(this.selectedYear)
        if (nextMonth > 12) {
          nextMonth = 1
          nextYear += 1
        }
        endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
      } else if (this.selectedYear) {
        // 只指定年份
        startDate = `${this.selectedYear}-01-01T00:00:00.000Z`
        endDate = `${parseInt(this.selectedYear) + 1}-01-01T00:00:00.000Z`
      } else {
        // 全部時間，設置一個較大的範圍
        startDate = '2017-01-01T00:00:00.000Z'
        endDate = '2030-12-31T23:59:59.999Z'
      }

      return { startDate, endDate }
    },

    // 一次性獲取分類數據
    async getAllCategoryData(retryCount = 0) {
      try {
        const timeRange = this.getTimeRange()
        const { startDate, endDate } = timeRange

        const params = {
          page: 1,
          pageSize: 10000, // 一万条，如果还有则再请求，这样不会超过最大请求量
          'filters[$and][0][createdAt][$gte]': startDate,
          'filters[$and][1][createdAt][$lte]': endDate,
          'filters[$and][2][event][$eq]': this.selectedCategoryType // 根據選擇的分類類型篩選
        }

        // 如果選擇了特定設備，添加設備篩選
        if (this.selectedDevice) {
          params['filters[$and][3][fid][$eq]'] = this.selectedDevice
        }

        console.log('分類查詢參數:', params)

        // 獲取第一頁數據
        const firstResponse = await getData(this.apiName, params)
        console.log('API響應結構:', firstResponse)
        console.log('第一頁數據:', firstResponse.results?.length || 0, '條記錄')
        console.log('分頁信息:', firstResponse.pagination)

        let allResults = firstResponse.results || []

        // 如果有更多頁面，繼續獲取
        if (firstResponse.pagination && firstResponse.pagination.pageCount > 1) {
          const totalPages = Math.min(firstResponse.pagination.pageCount, 10) // 限制最多10頁，避免請求過多
          console.log(`總共 ${firstResponse.pagination.pageCount} 頁，將獲取前 ${totalPages} 頁`)

          for (let page = 2; page <= totalPages; page++) {
            try {
              const pageParams = { ...params, page }
              const pageResponse = await getData(this.apiName, pageParams)
              if (pageResponse.results && pageResponse.results.length > 0) {
                allResults = allResults.concat(pageResponse.results)
                console.log(`第 ${page} 頁獲取到 ${pageResponse.results.length} 條記錄`)
              }
              // 添加小延遲避免請求過快
              await this.delay(200)
            } catch (error) {
              console.error(`獲取第 ${page} 頁數據失敗:`, error)
              break
            }
          }
        }

        console.log('總共獲取到分類數據:', allResults.length, '條記錄')
        return allResults
      } catch (error) {
        console.error(`獲取分類數據失敗 (嘗試 ${retryCount + 1}/${this.maxRetries + 1}):`, error)

        // 檢查是否為503錯誤或網絡錯誤，且還有重試次數
        if (retryCount < this.maxRetries && this.shouldRetry(error)) {
          console.log(`等待 ${this.retryDelay}ms 後重試...`)
          await this.delay(this.retryDelay)
          return this.getAllCategoryData(retryCount + 1)
        }

        return []
      }
    },

    // 本地處理分類數據，按分類名稱分組統計
    processCategoryData(rawData) {
      console.log('開始處理分類數據，原始數據量:', rawData.length)

      const categoryMap = new Map()
      let processedCount = 0
      let validRecords = 0

      // 處理原始數據，按分類名稱分組
      rawData.forEach(record => {
        processedCount++

        if (!record.name || !record.event || record.event !== this.selectedCategoryType) {
          return
        }

        const categoryName = record.name.trim() // 去除前後空格

        if (categoryMap.has(categoryName)) {
          categoryMap.set(categoryName, categoryMap.get(categoryName) + 1)
        } else {
          categoryMap.set(categoryName, 1)
        }

        validRecords++

        if (validRecords <= 10) { // 打印前10條有效記錄
          console.log(`有效記錄 ${validRecords}:`, {
            categoryName: categoryName,
            event: record.event,
            fid: record.fid,
            createdAt: record.createdAt
          })
        }
      })

      console.log(`處理完成: 總記錄 ${processedCount} 條，有效記錄 ${validRecords} 條`)
      console.log(`發現 ${categoryMap.size} 個不同的分類`)

      // 轉換為數組並按訪問次數排序（從多到少）
      const result = Array.from(categoryMap.entries()).map(([categoryName, visitCount]) => ({
        categoryName: categoryName,
        visitCount: visitCount
      })).sort((a, b) => b.visitCount - a.visitCount)

      console.log('分類訪問排行榜（前10名）:', result.slice(0, 10))

      return result
    },

    // 判斷是否應該重試
    shouldRetry(error) {
      // 503 Service Unavailable
      if (error.response && error.response.status === 503) {
        return true
      }
      // 502 Bad Gateway
      if (error.response && error.response.status === 502) {
        return true
      }
      // 網絡錯誤
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        return true
      }
      // 超時錯誤
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return true
      }
      return false
    },

    // 延遲函數，用於控制API調用頻率
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 生成分類訪問排行榜
    async generateTrendChart() {
      if (!this.selectedCategoryType) {
        this.$message.warning('請選擇分類類型')
        return
      }

      this.chartLoading = true

      try {
        const deviceText = this.selectedDevice ?
          `設備 ${this.getDeviceName(this.selectedDevice)} 的` : '所有設備的'
        const timeText = this.getTimeRangeText()

        this.$message.info(`正在獲取 ${timeText} ${deviceText}${this.getCategoryTypeLabel()}數據...`)

        // 獲取所有分類數據（支持分頁）
        const rawData = await this.getAllCategoryData()

        if (rawData.length === 0) {
          this.$message.warning('未獲取到任何分類數據，請檢查查詢條件')
          this.chartData = []
          this.tableData = []
          return
        }

        this.$message.info(`成功獲取到 ${rawData.length} 條分類記錄，正在按分類分組統計...`)

        // 本地處理數據，按分類名稱分組統計
        this.chartData = this.processCategoryData(rawData)

        // 生成表格數據
        this.tableData = [...this.chartData]

        this.$nextTick(() => {
          this.initTrendChart()
        })

        this.$message.success(`成功生成 ${this.chartData.length} 個分類的訪問排行榜！`)
      } catch (error) {
        console.error('生成分類訪問排行榜失敗:', error)
        this.$message.error('生成分類訪問排行榜失敗')
      } finally {
        this.chartLoading = false
      }
    },

    // 獲取時間範圍文本
    getTimeRangeText() {
      if (this.selectedYear && this.selectedMonth) {
        return `${this.selectedYear}年${parseInt(this.selectedMonth)}月`
      } else if (this.selectedYear) {
        return `${this.selectedYear}年`
      } else {
        return '全部時間'
      }
    },

    // 獲取設備名稱
    getDeviceName(deviceId) {
      const device = this.devices.find(d => d.machineId === deviceId)
      return device ? `${device.name} (${device.mall} ${device.floor})` : `設備${deviceId}`
    },

    // 初始化分類排行柱狀圖
    initTrendChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.trendChart, 'macarons')

      const topCategories = this.chartData
      const categoryNames = topCategories.map(item => item.categoryName)
      const visitCounts = topCategories.map(item => item.visitCount)

      const option = {
        title: {
          text: `${this.getCategoryTypeLabel()}訪問排行榜`,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          formatter: function(params) {
            const param = params[0]
            return `<div style="font-weight: bold; margin-bottom: 5px;">${param.name}</div>
                    <div style="margin: 2px 0;">
                      ${param.marker}<span style="display: inline-block; width: 80px;">訪問次數:</span>
                      <span style="font-weight: bold; color: #409EFF;">${param.value.toLocaleString()}</span>
                    </div>`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '12%',
          containLabel: true
        },
        dataZoom: [{
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: topCategories.length > 10 ? 50 : 100, // 如果分類數量超過10個，默認顯示50%
          bottom: '5%'
        }, {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: topCategories.length > 10 ? 50 : 100
        }],
        xAxis: {
          type: 'category',
          data: categoryNames,
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            interval: 0,
            rotate: 45, // 旋轉45度避免重疊
            formatter: function(value) {
              // 如果分類名稱太長，截斷並添加省略號
              if (value.length > 12) {
                return value.substring(0, 12) + '...'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '訪問次數',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(0) + 'k'
              }
              return value
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'solid'
            }
          }
        },
        series: [{
          name: '訪問次數',
          type: 'bar',
          data: visitCounts,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: '#409EFF'
              }, {
                offset: 1, color: '#66B3FF'
              }]
            },
            borderRadius: [4, 4, 0, 0] // 圓角頂部
          },
          label: {
            show: true,
            position: 'top',
            color: '#409EFF',
            fontSize: 11,
            fontWeight: 'bold',
            formatter: function(params) {
              if (params.value >= 1000) {
                return (params.value / 1000).toFixed(1) + 'k'
              }
              return params.value
            }
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#66B3FF'
                }, {
                  offset: 1, color: '#409EFF'
                }]
              }
            }
          }
        }]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 重置搜尋
    resetSearch() {
      // 重置分類類型
      this.selectedCategoryType = 'food_category'

      // 重置年份和月份選擇
      this.selectedYear = ''
      this.selectedMonth = ''

      // 重置設備選擇
      this.selectedDevice = ''

      // 清空圖表數據
      this.chartData = []
      this.tableData = []
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `${this.getCategoryTypeLabel()}訪問排行榜_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },

    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `${this.getCategoryTypeLabel()}訪問排行榜_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.tableData || this.tableData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      try {
        import('xlsx').then(XLSX => {
          const headers = ['排名', '分類名稱', '訪問次數']
          const ws_data = [
            headers,
            ...this.tableData.map((item, index) => [
              index + 1,
              item.categoryName,
              item.visitCount || 0
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, `${this.getCategoryTypeLabel()}訪問排行榜`)

          // 設置列寬
          ws['!cols'] = [
            { wch: 8 },  // 排名
            { wch: 30 }, // 分類名稱
            { wch: 15 }  // 訪問次數
          ]

          XLSX.writeFile(wb, `${this.getCategoryTypeLabel()}訪問排行榜_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch(() => {
          this.exportCSV()
        })
      } catch (error) {
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const headers = ['排名', '分類名稱', '訪問次數']
      const csvContent = [
        headers.join(','),
        ...this.tableData.map((item, index) => [
          index + 1,
          `"${item.categoryName}"`, // 用引號包圍分類名稱，避免逗號問題
          item.visitCount || 0
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `${this.getCategoryTypeLabel()}訪問排行榜_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-actions {
    .el-button {
      border: none;
      background: transparent;
      color: #606266;

      &:hover {
        color: #409EFF;
        background: rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 600px;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.summary-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;

  .summary-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
  }
}

.data-container {
  margin-top: 20px;
}
</style>
