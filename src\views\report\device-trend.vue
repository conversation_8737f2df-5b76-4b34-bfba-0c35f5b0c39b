<template>
  <div class="app-container">
    <h1>設備頁面訪問趨勢分析</h1>

    <!-- 篩選表單 -->
    <el-form ref="form" :model="form" label-width="100px" :inline="true" size="small">
      <!-- 時間維度選擇 -->
      <el-form-item label="時間維度">
        <el-select v-model="timeDimension" placeholder="請選擇時間維度" @change="handleTimeDimensionChange">
          <el-option label="按天" value="day" />
          <el-option label="按月" value="month" />
        </el-select>
      </el-form-item>

      <!-- 時間範圍選擇 -->
      <el-form-item label="時間範圍">
        <div>
          <el-date-picker
            v-if="timeDimension === 'day'"
            v-model="dayRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="dayPickerOptions"
          />
          <el-date-picker
            v-else
            v-model="monthRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="開始月份"
            end-placeholder="結束月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            :picker-options="monthPickerOptions"
          />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            <i class="el-icon-info"></i>
            最早可選{{ timeDimension === 'day' ? '日期' : '月份' }}：2025年5月{{ timeDimension === 'day' ? '1日' : '' }}
          </div>
        </div>
      </el-form-item>

      <!-- 並發控制設置 -->
      <!-- <el-form-item label="查詢模式">
        <el-select v-model="queryMode" placeholder="選擇查詢模式" style="width: 250px;">
          <el-option label="穩定查询模式" value="ultra-stable" />
          <el-option label="穩定模式（順序查詢）" value="sequential" />
          <el-option label="快速模式（小批量並發）" value="concurrent" />
        </el-select>
        <el-tooltip content="超穩定模式：間隔1秒；穩定模式：間隔500ms；快速模式：2個一批" placement="top">
          <i class="el-icon-question" style="margin-left: 5px; color: #909399;"></i>
        </el-tooltip>
      </el-form-item> -->

      <!-- 頁面選擇 -->
      <el-form-item label="頁面篩選">
        <el-select
          v-model="selectedPages"
          multiple
          collapse-tags
          placeholder="請選擇頁面（可多選）"
          style="width: 300px;"
        >
          <el-option
            v-for="page in pageConfig"
            :key="page.english"
            :label="page.chinese"
            :value="page.english"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          :loading="chartLoading"
          @click="generateTrendChart"
        >
          生成趨勢圖
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 圖表展示區域 -->
    <div class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>設備頁面訪問趨勢 - {{ timeDimension === 'day' ? '按天' : '按月' }}</span>
          <div class="chart-actions">
            <el-dropdown @command="handleExport" trigger="click">
              <el-button type="text" size="small" icon="el-icon-download">
                導出 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">
                  <i class="el-icon-picture"></i> Download PNG image
                </el-dropdown-item>
                <el-dropdown-item command="jpeg">
                  <i class="el-icon-picture"></i> Download JPEG image
                </el-dropdown-item>

                <el-dropdown-item command="xlsx">
                  <i class="el-icon-s-grid"></i> Download XLS
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div v-if="chartData.length > 0" class="chart-wrapper">
          <div ref="trendChart" class="trend-chart" />
        </div>
        <div v-else class="no-data">
          <p>暫無數據，請先設置篩選條件並生成趨勢圖</p>
          <p style="font-size: 12px; color: #666;">
            調試信息: chartData長度={{ chartData.length }}
          </p>
        </div>
      </el-card>
    </div>

    <!-- 統計摘要 -->
    <div v-if="chartData.length > 0" class="summary-container">
      <el-card>
        <div slot="header">
          <span>統計摘要</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">總訪問次數</div>
              <div class="summary-value">{{ totalVisits.toLocaleString() }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">平均日訪問量</div>
              <div class="summary-value">{{ averageDaily.toLocaleString() }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">最高單日訪問</div>
              <div class="summary-value">{{ maxDaily.toLocaleString() }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">活躍設備數</div>
              <div class="summary-value">{{ activeDevices }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 數據表格 -->
    <div v-if="tableData.length > 0" class="data-container">
      <el-card>
        <div slot="header">
          <span>詳細數據</span>
        </div>
        <el-table v-loading="chartLoading" :data="tableData" border stripe max-height="400">
          <el-table-column prop="date" label="日期" width="120" fixed="left" />
          <el-table-column
            v-for="page in selectedPages"
            :key="page"
            :prop="page"
            :label="getPageChineseName(page)"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{ (scope.row[page] || 0).toLocaleString() }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="total" label="總計" width="120">
            <template slot-scope="scope">
              <span style="font-weight: bold; color: #409EFF;">{{ (scope.row.total || 0).toLocaleString() }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'DeviceTrend',
  data() {
    return {
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: 'statistical', // 變量修改，請求主題名稱

      form: {},

      // 時間維度和範圍
      timeDimension: 'month', // day, month
      dayRange: [],
      monthRange: [],

      // 查詢控制
      queryMode: 'concurrent', // 查詢模式：ultra-stable(超穩定) 或 sequential(順序) 或 concurrent(並發)
      maxRetries: 3, // 最大重試次數
      retryDelay: 2000, // 重試延遲（毫秒）
      // 頁面相關
      selectedPages: ['home', 'food', 'shop', 'cinema', 'promotion', 'focalpoint', 'carsearch', 'transport', 'services', 'nearby'], // 默認選中的頁面
      pageConfig: [
        { chinese: "主頁", english: "home" },
        { chinese: "美食", english: "food" },
        { chinese: "購物", english: "shop" },
        { chinese: "戲院", english: "cinema" },
        { chinese: "精彩活動", english: "promotion" },
        { chinese: "主題設施", english: "focalpoint" },
        { chinese: "泊車", english: "carsearch" },
        { chinese: "交通資訊", english: "transport" },
        { chinese: "服務與設施", english: "services" },
        { chinese: "周邊遊", english: "nearby" }
      ],

      // 圖表相關
      chart: null, // echarts實例
      chartData: [], // 圖表數據
      tableData: [], // 表格數據
      chartLoading: false, // 圖表加載狀態

      // 日期選擇器配置
      dayPickerOptions: {
        // 設置最早可選日期為2025年5月1日
        disabledDate(time) {
          const minDate = new Date('2025-06-01')
          return time.getTime() < minDate.getTime()
        },
        shortcuts: [{
          text: '最近一週',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            // 確保開始日期不早於2025-06-01
            const minDate = new Date('2025-06-01')
            if (start < minDate) {
              start.setTime(minDate.getTime())
            }
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一個月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            // 確保開始日期不早於2025-06-01
            const minDate = new Date('2025-06-01')
            if (start < minDate) {
              start.setTime(minDate.getTime())
            }
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三個月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            // 確保開始日期不早於2025-06-01
            const minDate = new Date('2025-06-01')
            if (start < minDate) {
              start.setTime(minDate.getTime())
            }
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '從5月1日開始',
          onClick(picker) {
            const end = new Date()
            const start = new Date('2025-06-01')
            picker.$emit('pick', [start, end])
          }
        }]
      },

      monthPickerOptions: {
        // 設置最早可選月份為2025年5月
        disabledDate(time) {
          const minDate = new Date('2025-06-01')
          return time.getTime() < minDate.getTime()
        },
        shortcuts: [{
          text: '最近6個月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 6)
            // 確保開始月份不早於2025-05
            const minDate = new Date('2025-06-01')
            if (start < minDate) {
              start.setTime(minDate.getTime())
            }
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近12個月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setMonth(start.getMonth() - 12)
            // 確保開始月份不早於2025-05
            const minDate = new Date('2025-06-01')
            if (start < minDate) {
              start.setTime(minDate.getTime())
            }
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '從2025年5月開始',
          onClick(picker) {
            const end = new Date()
            const start = new Date('2025-06-01')
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {
    // 計算統計摘要
    totalVisits() {
      if (this.chartData.length === 0) return 0
      return this.chartData.reduce((sum, item) => sum + (item.total || 0), 0)
    },

    averageDaily() {
      if (this.chartData.length === 0) return 0
      return Math.round(this.totalVisits / this.chartData.length)
    },

    maxDaily() {
      if (this.chartData.length === 0) return 0
      return Math.max(...this.chartData.map(item => item.total || 0))
    },

    activeDevices() {
      // 由於現在不區分設備，這裡返回一個固定值或者從API獲取
      return '所有設備'
    }
  },
  created() {
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl

    // 設置默認時間範圍
    this.setDefaultTimeRange()
  },
  mounted() {
    // 監聽來自 iframe 的消息
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    // 移除消息監聽
    window.removeEventListener('message', this.handleMessage)
    // 銷毀圖表實例
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 處理消息
    handleMessage(event) {
      // 處理來自iframe的消息
    },

    // 設置默認時間範圍
    setDefaultTimeRange() {
      const end = new Date()
      const start = new Date()
      const minDate = new Date('2025-06-01') // 最早可選日期

      if (this.timeDimension === 'day') {
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30) // 最近30天

        // 確保開始日期不早於2025年5月1日
        if (start < minDate) {
          start.setTime(minDate.getTime())
        }

        this.dayRange = [
          start.toISOString().split('T')[0],
          end.toISOString().split('T')[0]
        ]
      } else {
        start.setMonth(start.getMonth() - 6) // 最近6個月

        // 確保開始月份不早於2025年5月
        if (start < minDate) {
          start.setTime(minDate.getTime())
        }

        this.monthRange = [
          start.toISOString().substr(0, 7),
          end.toISOString().substr(0, 7)
        ]
      }
    },

    // 時間維度變化處理
    handleTimeDimensionChange() {
      this.setDefaultTimeRange()
      // 清空圖表數據
      this.chartData = []
      this.tableData = []
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },



    // 獲取頁面中文名稱
    getPageChineseName(englishName) {
      const page = this.pageConfig.find(p => p.english === englishName)
      return page ? page.chinese : englishName
    },

    // 生成日期範圍
    generateDateRange() {
      const dates = []
      let current, end, format

      if (this.timeDimension === 'day') {
        if (!this.dayRange || this.dayRange.length !== 2) {
          this.$message.warning('請選擇日期範圍')
          return []
        }
        current = new Date(this.dayRange[0])
        end = new Date(this.dayRange[1])
        format = 'YYYY-MM-DD'
      } else {
        if (!this.monthRange || this.monthRange.length !== 2) {
          this.$message.warning('請選擇月份範圍')
          return []
        }
        current = new Date(this.monthRange[0] + '-01')
        end = new Date(this.monthRange[1] + '-01')
        format = 'YYYY-MM'
      }

      while (current <= end) {
        if (this.timeDimension === 'day') {
          dates.push(current.toISOString().split('T')[0])
          current.setDate(current.getDate() + 1)
        } else {
          dates.push(current.toISOString().substr(0, 7))
          current.setMonth(current.getMonth() + 1)
        }
      }

      return dates
    },

    // 獲取特定日期和頁面的訪問數據（所有設備）- 帶重試機制
    async getPageData(pageKey, date, retryCount = 0) {
      try {
        let startDate, endDate

        if (this.timeDimension === 'day') {
          startDate = `${date}T00:00:00.000Z`
          endDate = `${date}T23:59:59.999Z`
        } else {
          // 月份查詢
          const year = date.split('-')[0]
          const month = date.split('-')[1]
          startDate = `${year}-${month}-01T00:00:00.000Z`

          // 計算下個月的第一天
          let nextMonth = parseInt(month) + 1
          let nextYear = parseInt(year)
          if (nextMonth > 12) {
            nextMonth = 1
            nextYear += 1
          }
          endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
        }

        const params = {
          page: 1,
          pageSize: 1, // 只需要獲取總數
          'filters[$and][0][createdAt][$gte]': startDate,
          'filters[$and][1][createdAt][$lte]': endDate,
          'filters[$and][2][event_target][$contains]': pageKey
        }

        const response = await getData(this.apiName, params)
        return response.pagination.total
      } catch (error) {
        console.error(`獲取頁面 ${pageKey} 在 ${date} 的數據失敗 (嘗試 ${retryCount + 1}/${this.maxRetries + 1}):`, error)

        // 檢查是否為503錯誤或網絡錯誤，且還有重試次數
        if (retryCount < this.maxRetries && this.shouldRetry(error)) {
          console.log(`等待 ${this.retryDelay}ms 後重試...`)
          await this.delay(this.retryDelay)
          return this.getPageData(pageKey, date, retryCount + 1)
        }

        return 0
      }
    },

    // 判斷是否應該重試
    shouldRetry(error) {
      // 503 Service Unavailable
      if (error.response && error.response.status === 503) {
        return true
      }
      // 502 Bad Gateway
      if (error.response && error.response.status === 502) {
        return true
      }
      // 網絡錯誤
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        return true
      }
      // 超時錯誤
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return true
      }
      return false
    },

    // 延遲函數，用於控制API調用頻率
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 順序查詢（避免並發限制）- 增強版
    async sequentialQuery(tasks) {
      const results = []
      const delayMs = 500 // 增加到500ms間隔，避免503錯誤

      for (let i = 0; i < tasks.length; i++) {
        try {
          // 執行任務（已包含重試機制）
          const result = await this.getPageData(tasks[i].pageKey, tasks[i].date)
          results.push(result)

          // 顯示進度
          if (i % 3 === 0 || i === tasks.length - 1) {
            this.$message.info(`查詢進度: ${i + 1}/${tasks.length} (穩定模式)`)
          }

          // 添加延遲，避免API限制
          if (i < tasks.length - 1) {
            await this.delay(delayMs)
          }
        } catch (error) {
          console.error(`順序查詢第 ${i + 1} 個任務失敗:`, error)
          results.push(0)
        }
      }

      return results
    },

    // 並發查詢（分批處理）- 保守版
    async concurrentQuery(tasks) {
      const batchSize = 2 // 減少到每批2個請求，更保守
      const results = []

      for (let i = 0; i < tasks.length; i += batchSize) {
        const batch = tasks.slice(i, i + batchSize)

        try {
          // 使用帶重試機制的方法
          const batchPromises = batch.map(task => this.getPageData(task.pageKey, task.date))
          const batchResults = await Promise.all(batchPromises)
          results.push(...batchResults)

          // 顯示進度
          this.$message.info(`查詢進度: ${Math.min(i + batchSize, tasks.length)}/${tasks.length} (快速模式)`)

          // 批次間添加更長延遲
          if (i + batchSize < tasks.length) {
            await this.delay(1000) // 增加到1秒延遲
          }
        } catch (error) {
          console.error(`並發查詢批次 ${Math.floor(i / batchSize) + 1} 失敗:`, error)
          // 如果並發失敗，用0填充這批結果
          results.push(...new Array(batch.length).fill(0))
        }
      }

      return results
    },

    // 超穩定查詢（最保守的方式）
    async ultraStableQuery(tasks) {
      const results = []
      const delayMs = 500 // 1秒間隔，最保守

      for (let i = 0; i < tasks.length; i++) {
        try {
          // 執行任務（已包含重試機制）
          const result = await this.getPageData(tasks[i].pageKey, tasks[i].date)
          results.push(result)

          // 顯示進度
          if (i % 2 === 0 || i === tasks.length - 1) {
            this.$message.info(`查詢進度: ${i + 1}/${tasks.length} (超穩定模式 )`)
          }

          // 添加1秒延遲，確保不會觸發503錯誤
          if (i < tasks.length - 1) {
            await this.delay(delayMs)
          }
        } catch (error) {
          console.error(`超穩定查詢第 ${i + 1} 個任務失敗:`, error)
          results.push(0)
        }
      }

      return results
    },

    // 生成趨勢圖
    async generateTrendChart() {
      if (this.selectedPages.length === 0) {
        this.$message.warning('請至少選擇一個頁面')
        return
      }

      this.chartLoading = true

      try {
        const dates = this.generateDateRange()
        if (dates.length === 0) {
          return
        }

        this.$message.info(`正在查詢 ${this.selectedPages.length} 個頁面在 ${dates.length} 個時間點的數據...`)

        // 構建所有查詢任務（不預先創建promise）
        const tasks = []
        for (const date of dates) {
          for (const pageKey of this.selectedPages) {
            tasks.push({
              date,
              pageKey
            })
          }
        }

        // 根據查詢模式執行查詢
        let results
        if (this.queryMode === 'ultra-stable') {
          this.$message.info('正在查詢，請耐心等待...')
          results = await this.ultraStableQuery(tasks)
        } else if (this.queryMode === 'sequential') {
          this.$message.info('使用穩定模式查詢，間隔500ms，請耐心等待...')
          results = await this.sequentialQuery(tasks)
        } else {
          this.$message.info('使用快速模式查詢，小批量並發...')
          results = await this.concurrentQuery(tasks)
        }

        // 處理結果數據
        const dataMap = new Map()

        tasks.forEach((task, index) => {
          const key = task.date
          if (!dataMap.has(key)) {
            dataMap.set(key, {
              date: task.date,
              total: 0
            })
          }

          const item = dataMap.get(key)
          item[task.pageKey] = results[index]
          item.total += results[index]
        })

        // 轉換為數組並排序
        this.chartData = Array.from(dataMap.values()).sort((a, b) => {
          return a.date.localeCompare(b.date)
        })

        // 生成表格數據
        this.tableData = [...this.chartData]

        this.$nextTick(() => {
          this.initTrendChart()
        })

        this.$message.success(`成功生成 ${this.chartData.length} 條趨勢數據`)
      } catch (error) {
        console.error('生成趨勢圖失敗:', error)
        this.$message.error('生成趨勢圖失敗')
      } finally {
        this.chartLoading = false
      }
    },

    // 初始化趨勢圖
    initTrendChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.trendChart, 'macarons')

      // 準備數據
      const dates = [...new Set(this.chartData.map(item => item.date))].sort()

      // 為每個頁面創建一條線（聚合所有選中設備的數據）
      const series = []
      const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']

      // 添加"所有頁面"總計線
      const allPagesData = dates.map(date => {
        const item = this.chartData.find(d => d.date === date)
        return item ? item.total : 0
      })

      series.push({
        name: '所有頁面',
        type: 'line',
        data: allPagesData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: colors[0]
        },
        itemStyle: {
          color: colors[0]
        },
        label: {
          show: true,
          position: 'top',
          color: colors[0],
          fontSize: 11,
          fontWeight: 'bold',
          formatter: function(params) {
            if (params.value >= 1000) {
              return (params.value / 1000).toFixed(1) + 'k'
            }
            return params.value
          }
        },
        emphasis: {
          focus: 'series',
          label: {
            show: true
          }
        }
      })

      // 為每個選中的頁面創建一條線
      this.selectedPages.forEach((pageKey, index) => {
        const pageName = this.getPageChineseName(pageKey)

        const data = dates.map(date => {
          const item = this.chartData.find(d => d.date === date)
          return item ? (item[pageKey] || 0) : 0
        })

        series.push({
          name: pageName,
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 2,
            color: colors[(index + 1) % colors.length]
          },
          itemStyle: {
            color: colors[(index + 1) % colors.length]
          },
          label: {
            show: false, // 默認不顯示，避免圖表過於擁擠
            position: 'top',
            color: colors[(index + 1) % colors.length],
            fontSize: 10,
            formatter: function(params) {
              if (params.value >= 1000) {
                return (params.value / 1000).toFixed(1) + 'k'
              }
              return params.value
            }
          },
          emphasis: {
            focus: 'series',
            label: {
              show: true
            }
          }
        })
      })

      const option = {
        title: {
          text: `頁面訪問趨勢 ${new Date().getFullYear()}`,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#999',
              width: 1,
              type: 'dashed'
            }
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          formatter: function(params) {
            let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`
            params.forEach(param => {
              result += `<div style="margin: 2px 0;">
                ${param.marker}<span style="display: inline-block; width: 80px;">${param.seriesName}:</span>
                <span style="font-weight: bold;">${param.value.toLocaleString()}</span>
              </div>`
            })
            return result
          }
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          bottom: 10,
          data: series.map(s => s.name),
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '12%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates.map(date => {
            if (this.timeDimension === 'month') {
              const [year, month] = date.split('-')
              const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
              return monthNames[parseInt(month) - 1]
            } else {
              return date.split('-').slice(1).join('/')
            }
          }),
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '訪問次數',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(0) + 'k'
              }
              return value
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'solid'
            }
          }
        },
        series: series
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 重置搜尋
    resetSearch() {
      // 重置時間範圍
      this.setDefaultTimeRange()

      // 重置頁面選擇
      this.selectedPages = ['home', 'food', 'shop']

      // 重置查詢模式
      this.queryMode = 'ultra-stable'

      // 清空圖表數據
      this.chartData = []
      this.tableData = []
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'pdf':
            this.exportPDF()
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `設備頁面訪問趨勢_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },


    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `設備頁面訪問趨勢_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.tableData || this.tableData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      try {
        import('xlsx').then(XLSX => {
          const headers = ['日期', '設備名稱', '設備ID', ...this.selectedPages.map(page => this.getPageChineseName(page)), '總計']
          const ws_data = [
            headers,
            ...this.tableData.map(item => [
              item.date,
              item.deviceName,
              item.deviceId,
              ...this.selectedPages.map(page => item[page] || 0),
              item.total
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '設備頁面訪問趨勢')

          // 設置列寬
          ws['!cols'] = [
            { wch: 12 }, // 日期
            { wch: 20 }, // 設備名稱
            { wch: 10 }, // 設備ID
            ...this.selectedPages.map(() => ({ wch: 10 })), // 各頁面
            { wch: 10 }  // 總計
          ]

          XLSX.writeFile(wb, `設備頁面訪問趨勢_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch(() => {
          this.exportCSV()
        })
      } catch (error) {
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const headers = ['日期', '設備名稱', '設備ID', ...this.selectedPages.map(page => this.getPageChineseName(page)), '總計']
      const csvContent = [
        headers.join(','),
        ...this.tableData.map(item => [
          item.date,
          `"${item.deviceName}"`,
          item.deviceId,
          ...this.selectedPages.map(page => item[page] || 0),
          item.total
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `設備頁面訪問趨勢_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-actions {
    .el-button {
      border: none;
      background: transparent;
      color: #606266;

      &:hover {
        color: #409EFF;
        background: rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 600px;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.summary-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;

  .summary-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
  }
}

.data-container {
  margin-top: 20px;
}
</style>