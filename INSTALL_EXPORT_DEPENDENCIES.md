# 圖表導出功能依賴安裝指南

## 問題說明

當前圖表導出功能已經實現了基礎功能，但為了獲得完整的導出體驗，建議安裝以下可選依賴庫：

## 當前狀態

✅ **已可用的功能**（無需安裝依賴）：
- PNG圖片導出
- JPEG圖片導出  
- SVG向量圖導出

⚠️ **需要依賴的功能**：
- PDF文檔導出（需要 jspdf）
- Excel文件導出（需要 xlsx）

## 備用方案

如果不安裝依賴庫，系統會自動使用備用方案：
- **PDF導出** → 自動降級為PNG圖片導出
- **Excel導出** → 自動降級為CSV文件導出

## 安裝依賴庫

### 方案一：安裝完整功能依賴

```bash
# 安裝PDF導出依賴
npm install jspdf

# 安裝Excel導出依賴  
npm install xlsx
```

### 方案二：僅安裝PDF功能

```bash
npm install jspdf
```

### 方案三：僅安裝Excel功能

```bash
npm install xlsx
```

## 安裝後的功能

### 安裝 jspdf 後
✅ **PDF導出功能**：
- 自動檢測圖表方向（橫向/縱向）
- 高質量PDF文檔生成
- 適合正式報告和存檔

### 安裝 xlsx 後  
✅ **Excel導出功能**：
- 完整的數據表格導出
- 自動設置列寬
- 包含所有圖表數據（設備ID、名稱、商場、樓層、記錄數）

## 驗證安裝

安裝依賴後，重新啟動開發服務器：

```bash
npm run dev
```

然後測試導出功能：
1. 生成設備統計圖表
2. 點擊導出按鈕
3. 選擇PDF或Excel格式
4. 檢查是否正常下載

## 錯誤處理

### 如果仍然報錯

1. **清除緩存**：
```bash
npm run clean
npm install
npm run dev
```

2. **檢查版本兼容性**：
```bash
npm list jspdf
npm list xlsx
```

3. **重新安裝**：
```bash
npm uninstall jspdf xlsx
npm install jspdf xlsx
```

## 推薦的依賴版本

```json
{
  "dependencies": {
    "jspdf": "^2.5.1",
    "xlsx": "^0.18.5"
  }
}
```

## 不安裝依賴的影響

如果選擇不安裝依賴庫，功能影響如下：

### PDF導出
- ❌ 無法生成真正的PDF文檔
- ✅ 會自動導出PNG圖片作為替代
- ✅ 用戶可以手動將PNG轉換為PDF

### Excel導出
- ❌ 無法生成Excel文件
- ✅ 會自動導出CSV文件作為替代
- ✅ CSV文件可以用Excel打開

## 建議

### 對於生產環境
**強烈建議安裝所有依賴**，以提供完整的用戶體驗：
```bash
npm install jspdf xlsx
```

### 對於開發測試
可以先不安裝依賴，使用備用方案進行功能測試。

### 對於特定需求
- 如果只需要圖片導出：無需安裝任何依賴
- 如果需要文檔導出：安裝 jspdf
- 如果需要數據導出：安裝 xlsx

## 技術說明

### 動態導入
代碼使用ES2020的動態導入語法：
```javascript
import('jspdf').then(({ default: jsPDF }) => {
  // PDF導出邏輯
}).catch((error) => {
  // 自動降級到備用方案
})
```

### 優勢
- **按需加載**：只有在使用時才加載庫
- **優雅降級**：庫不存在時自動使用備用方案
- **無阻塞**：不影響其他功能的正常使用

### 文件大小影響
- **jspdf**: ~200KB (gzipped)
- **xlsx**: ~400KB (gzipped)
- **總增加**: ~600KB

## 常見問題

### Q: 是否必須安裝這些依賴？
A: 不是必須的。基礎的PNG、JPEG、SVG導出功能無需依賴即可使用。

### Q: 安裝後是否會影響打包大小？
A: 由於使用動態導入，只有在實際使用導出功能時才會加載這些庫，對初始加載影響較小。

### Q: 可以只安裝其中一個嗎？
A: 可以。可以根據實際需求選擇性安裝。

### Q: 如何確認依賴是否安裝成功？
A: 安裝後重啟服務器，測試對應的導出功能是否正常工作。

## 總結

- ✅ **當前可用**：PNG、JPEG、SVG導出
- 🔧 **可選增強**：安裝依賴獲得PDF和Excel導出
- 🛡️ **備用保障**：即使不安裝依賴也有替代方案
- 📈 **推薦做法**：生產環境安裝完整依賴
