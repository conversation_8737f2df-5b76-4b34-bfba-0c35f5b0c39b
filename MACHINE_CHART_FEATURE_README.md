# 设备统计图表功能实现

## 功能概述

实现了一个高效的设备记录统计图表功能，通过先获取设备列表，再分别查询每个设备的记录总数来生成柱状图，避免了一次性加载大量数据的性能问题。

## 核心优势

### 1. 性能优化
- **分页查询**: 每个设备只查询 `pageSize=1`，仅获取 `pagination.total` 统计数据
- **并发请求**: 使用 `Promise.all()` 并发查询所有设备，提高查询效率
- **数据过滤**: 只显示有记录的设备，减少图表复杂度

### 2. 完整的时间筛选支持
- **年份筛选**: 支持按年份筛选设备记录
- **月份筛选**: 支持按月份筛选设备记录  
- **日期范围筛选**: 支持自定义日期范围筛选

### 3. 丰富的设备信息展示
- **设备名称**: 显示设备的友好名称
- **商场信息**: 显示设备所在商场
- **楼层信息**: 显示设备所在楼层
- **记录统计**: 显示设备的记录总数

## 技术实现

### API接口使用

#### 1. 设备列表接口
```
GET https://yohomall.esc-map.com/api/machines
```

**响应格式:**
```javascript
{
  data: [
    {
      id: 1,
      name: "machine1",
      machineId: "1", 
      point: "-1768.5062865546624, 40020, -434.16526311556026",
      createdAt: "2024-06-02T03:56:01.988Z",
      updatedAt: "2024-08-19T14:29:53.078Z",
      desc: "yoho mix 1f",
      mall: "YOHO MIX",
      floor: "L1",
      type: "1"
    }
  ]
}
```

#### 2. 设备记录统计接口
```
GET /api/statistical?page=1&pageSize=1&filters[$and][2][fid][$eq]=14&[时间筛选参数]
```

**响应格式:**
```javascript
{
  "results": [],
  "pagination": {
    "page": 1,
    "pageSize": 1,
    "pageCount": 30,
    "total": 1486  // 这是我们需要的统计数据
  }
}
```

### 核心方法实现

#### 1. getMachines()
```javascript
async getMachines() {
  try {
    const response = await fetch('https://yohomall.esc-map.com/api/machines')
    const data = await response.json()
    this.machines = data.data || []
    return this.machines
  } catch (error) {
    console.error('获取设备列表失败:', error)
    this.$message.error('获取设备列表失败')
    return []
  }
}
```

#### 2. getMachineRecordCount(machineId)
```javascript
async getMachineRecordCount(machineId) {
  try {
    const params = {
      page: 1,
      pageSize: 1,
      'filters[$and][2][fid][$eq]': machineId, // 关键的fid筛选语法
      ...this.getTimeFilterParams()
    }
    
    const response = await getData(this.apiName, params)
    return {
      machineId,
      total: response.pagination.total
    }
  } catch (error) {
    return { machineId, total: 0 }
  }
}
```

#### 3. generateMachineChart()
```javascript
async generateMachineChart() {
  this.machineChartLoading = true
  
  try {
    // 1. 获取设备列表
    const machines = this.machines.length > 0 ? this.machines : await this.getMachines()
    
    // 2. 并发查询每个设备的记录总数
    const promises = machines.map(machine => 
      this.getMachineRecordCount(machine.machineId)
    )
    const results = await Promise.all(promises)
    
    // 3. 处理数据并生成图表
    const chartData = results
      .filter(result => result.total > 0)
      .map(result => {
        const machine = machines.find(m => m.machineId === result.machineId)
        return {
          fid: result.machineId,
          name: machine ? machine.name : `设备${result.machineId}`,
          mall: machine ? machine.mall : '',
          floor: machine ? machine.floor : '',
          count: result.total
        }
      })
      .sort((a, b) => b.count - a.count)
    
    this.chartData = chartData
    this.$nextTick(() => {
      this.initMachineChart(chartData)
    })
  } catch (error) {
    this.$message.error('生成设备统计图表失败')
  } finally {
    this.machineChartLoading = false
  }
}
```

### Strapi筛选语法

#### fid筛选
```
filters[$and][2][fid][$eq]=14
```

#### 时间筛选组合
```
// 年份筛选 (2025年)
filters[$and][0][createdAt][$gte]=2025-01-01T00:00:00.000Z
filters[$and][1][createdAt][$lt]=2026-01-01T00:00:00.000Z
filters[$and][2][fid][$eq]=14

// 月份筛选 (2025年5月)
filters[$and][0][createdAt][$gte]=2025-05-01T00:00:00.000Z
filters[$and][1][createdAt][$lt]=2025-06-01T00:00:00.000Z
filters[$and][2][fid][$eq]=14

// 日期范围筛选
filters[$and][0][createdAt][$gte]=2025-05-01T00:00:00.000Z
filters[$and][1][createdAt][$lte]=2025-05-31T23:59:59.999Z
filters[$and][2][fid][$eq]=14
```

## 图表特性

### 1. 数据展示
- **设备名称**: X轴显示设备名称和位置信息
- **记录数量**: Y轴显示记录总数，支持k单位显示
- **排序**: 按记录数量降序排列
- **颜色**: 多彩配色方案，每个设备不同颜色

### 2. 交互功能
- **悬停提示**: 显示设备详细信息（名称、ID、商场、楼层、记录数）
- **响应式**: 自适应窗口大小变化
- **数值格式化**: 大数值自动显示为k单位

### 3. 用户体验
- **加载状态**: 显示加载进度和状态提示
- **错误处理**: 完善的错误提示和异常处理
- **进度反馈**: 显示查询进度信息

## 使用方法

### 1. 基本使用
1. 打开Report页面
2. 选择时间筛选条件（可选）
3. 点击"生成设备统计图表"按钮
4. 等待数据加载完成
5. 查看生成的柱状图

### 2. 时间筛选使用
1. 选择筛选类型（年份/月份/日期范围）
2. 设置相应的时间值
3. 点击"生成设备统计图表"
4. 图表将显示指定时间范围内的设备记录统计

## 性能考虑

### 1. 查询优化
- 每个设备查询使用 `pageSize=1`，最小化数据传输
- 并发查询所有设备，减少总查询时间
- 只显示有记录的设备，减少图表复杂度

### 2. 内存管理
- 组件销毁时自动清理图表实例
- 避免内存泄漏

### 3. 用户体验
- 显示查询进度信息
- 提供加载状态反馈
- 完善的错误处理机制

## 示例场景

### 场景1: 查看所有设备活跃度
```
1. 不设置时间筛选
2. 点击"生成设备统计图表"
3. 查看所有设备的总记录数排名
```

### 场景2: 分析月度设备使用情况
```
1. 选择筛选类型为"月份"
2. 选择"2025-05"
3. 点击"生成设备统计图表"
4. 查看5月份各设备的使用情况
```

### 场景3: 对比特定时间段设备表现
```
1. 选择筛选类型为"日期范围"
2. 设置开始和结束日期
3. 点击"生成设备统计图表"
4. 分析指定时间段内的设备活跃度
```

## 注意事项

1. **网络依赖**: 需要访问外部API获取设备列表
2. **数据量**: 设备数量较多时查询时间会相应增加
3. **时间格式**: 所有时间都转换为UTC格式
4. **筛选逻辑**: fid筛选使用 `[$and][2]` 索引，避免与时间筛选冲突
