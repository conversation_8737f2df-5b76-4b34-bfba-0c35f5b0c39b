<template>
  <div class="app-container">
    <!-- <h1>條文管理</h1> -->
    <h1>{{$t('route.Clause')}}</h1>
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="">
        <el-input v-model="listQuery._q" :placeholder="$t('table.query')" />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">{{$t('table.search')}}</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">{{$t('table.reset')}}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate()">{{$t('table.add')}}</el-button>
        <el-button type="primary" plain icon="el-icon-download" @click="handleDownload()">Export Excel</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" border stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" :label="$t('table.name2')" />
      <el-table-column prop="language" :label="$t('table.language')" width="120" />
      <el-table-column prop="detail" min-width="70px" :label="$t('table.clause')"  width="420">
        <template slot-scope="{row}">
          <template>
            <span style="white-space:pre-line">{{ row.detail }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="updatedAt" :label="$t('table.editDate')" />
      <el-table-column prop="updatedBy" :label="$t('table.editor')" />
      <el-table-column prop="stateType" :label="$t('table.status')" />
      <el-table-column :label="$t('table.operate')">
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" size="small" @click="handleClick(scope.row)">{{$t('table.edit')}}</el-button>
          <el-popconfirm :confirm-button-text="$t('table.cancel')" :cancel-button-text="$t('table.delete')" icon="el-icon-info" icon-color="red"
            :title="$t('table.deleteText')" @onCancel="deleteDialog(scope.row)">
            <el-button slot="reference" type="danger" size="small" icon="el-icon-delete" style="margin-left:12px">{{$t('table.delete')}}
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>

    </el-table>

    <Pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.pageSize"
      @pagination="getList(listQuery)" />
    <el-dialog :title="textMap[dialogStatus] == '修改'?$t('table.edit'):$t('table.create')" :visible.sync="dialogFormVisible">
      <el-form ref="form" :model="form" label-position="right" label-width="70px"
        style="width: 600px; margin-left:50px;">

        <el-form-item :label="$t('table.name2')"  prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item :label="$t('table.language')" prop="language">
          <el-input v-model="form.language" />
        </el-form-item>
        <el-form-item :label="$t('table.clause')">
          <el-input v-model="form.detail" :autosize="{ minRows: 4, maxRows: 20 }" type="textarea"
            placeholder="Please input" />
        </el-form-item>
        <el-form-item :label="$t('table.status')">
          <el-select v-model="form.stateType" class="filter-item" placeholder="Please select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{$t('table.cancel')}}
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          {{ dialogStatus === 'create' ? $t('table.create'): $t('table.edit')}}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getData, postData } from '@/api/product'
import { timeChange } from '@/api/timeChange'
import Pagination from '@/components/Pagination'

export default {
  name: 'Clause',
  components: { Pagination },
  data() {
    return {
      baseUrl: '/content-manager/collection-types/api::clause.clause/',
      list: [],
      totle: 0,
      listLoading: false,
      // 弹框
      dialogFormVisible: false,
      // 状态
      statusOptions: [{
        value: '1',
        label: this.$t('table.status1')
      }, {
        value: '0',
        label: this.$t('table.status0')
      }],
      // 弹框属于新增还是修改
      dialogStatus: '',
      textMap: {
        update: '修改',
        create: '新增'
      },
      parms: {
        searchName: ''
      },
      form: {
        name: '',
        remark: '',
        state: ''
      },
      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 20,
        sort: 'id:ASC',
        _q: ''
      }
    }
  },
  created() {
    this.getList(this.listQuery)
  },
  methods: {
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.baseUrl, params).then(response => {
        const res = response.results
        const arrList = []
        // 把返回数据转换为table需要的格式
        for (let index = 0; index < res.length; index++) {
          const element = res[index]
          const arr = {
            id: res[index].id,
            name: res[index].name,
            language: res[index].language,
            detail: res[index].detail,
            state: res[index].state,
            stateType: res[index].state ? this.$t('table.status1') : this.$t('table.status0'),
            updatedAt: timeChange(res[index].updatedAt),
            updatedBy: res[index].updatedBy? res[index].updatedBy.firstname: 'null'
          }
          arrList.push(arr)
        }
        this.list = arrList
        this.total = response.pagination.total
        this.listLoading = false
      })
    },
    // 點擊編輯按鈕後的彈框
    handleClick(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      const rowForm = {
        id: row.id,
        name: row.name,
        language: row.language,
        detail: row.detail,
        stateType: row.stateType,
        state: row.stateType == '啟用'
      }
      this.form = Object.assign({}, rowForm) // copy obj

      console.log(this.form.state)
    },

    // 點擊編輯按鈕進行修改的事件
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            id: this.form.id,
            name: this.form.name,
            language: this.form.language,
            detail: this.form.detail,
            state: this.form.stateType.value == '1'
          }
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'put', temp.id, tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 清空提交表
    resetForm() {
      this.form = {
        name: '',
        remark: '',
        state: true,
        stateType: '啟用'
      }
    },
    // 创建的按钮打开
    handleCreate() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })
    },
    // 點擊創建後的事件
    createData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const temp = {
            name: this.form.name,
            language: this.form.language,
            detail: this.form.detail,
            state: this.form.stateType.value != '0'
          }
          console.log(temp.state)
          const tempData = Object.assign({}, temp)
          console.log(tempData)
          postData(this.baseUrl, 'post', '', tempData).then(() => {
            this.getList(this.listQuery)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Create Successfully',
              type: 'success',
              duration: 1000
            })
          })
        }
      })
    },
    // 删除的最后确认按钮
    deleteDialog(row) {
      console.log('row', row)
      this.$confirm(this.$t('table.deleteText'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        console.log(row.id)
        postData(this.baseUrl, 'delete', row.id, '').then(() => {
          this.getList(this.listQuery)
          this.dialogFormVisible = false
          this.$notify({
            title: 'Success',
            message: 'Delete Successfully',
            type: 'success',
            duration: 1000
          })
        })
        this.$message({
          type: 'success',
          message: this.$t('table.deleteSuccess')
        })
        this.scopeId = ''
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.deleteCancel')
        })
      })
    },
    // 搜索
    handleSearch() {
      this.listQuery.page = 1
      console.log(this.listQuery)
      this.getList(this.listQuery)
    },

    // 重置
    resetSearch() {
      this.listQuery._q  = ''
      this.getList(this.listQuery)
    },

    // 導出Excel
    handleDownload() {
      this.$confirm(this.$t('table.goonPrompt'), this.$t('table.prompt'), {
        cancelButtonText: this.$t('table.cancel'),
        confirmButtonText: this.$t('table.confirm'),
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['ID',this.$t('table.name2'),this.$t('table.language'),this.$t('table.clause'),this.$t('table.editDate'),this.$t('table.editor'),this.$t('table.status')]
          const filterVal = ['id','name','language','detail','updatedAt','updatedBy','stateType']
          const data = this.formatJson(filterVal, this.list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.$t('route.Clause'),
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.downloadLoading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('table.cancelPrompt')
        })
      })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },

  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}
</style>
