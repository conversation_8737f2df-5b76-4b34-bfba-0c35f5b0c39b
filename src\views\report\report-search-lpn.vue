<template>
  <div class="app-container">
    <!-- 查詢條件 -->
    <el-card>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <!-- 時間篩選類型 -->
        <el-form-item label="篩選類型">
          <el-select v-model="filterType" placeholder="請選擇篩選類型" @change="handleFilterTypeChange">
            <el-option label="年份" value="year" />
            <el-option label="月份" value="month" />
            <el-option label="日期範圍" value="daterange" />
          </el-select>
        </el-form-item>

        <!-- 年份篩選 -->
        <el-form-item v-if="filterType === 'year'" label="年份">
          <el-date-picker
            v-model="queryYear"
            type="year"
            placeholder="選擇年份"
            format="yyyy"
            value-format="yyyy"
          />
        </el-form-item>

        <!-- 月份篩選 -->
        <el-form-item v-if="filterType === 'month'" label="月份">
          <el-date-picker
            v-model="queryMonth"
            type="month"
            placeholder="選擇月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
          />
        </el-form-item>

        <!-- 日期範圍篩選 -->
        <el-form-item v-if="filterType === 'daterange'" label="日期範圍">
          <el-date-picker
            v-model="queryDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <!-- 設備選擇 -->
        <el-form-item label="選擇設備">
          <el-select
            v-model="selectedDevice"
            placeholder="選擇設備（默認全部）"
            clearable
            filterable
            style="width: 200px;"
          >
            <el-option label="全部設備" value="" />
            <el-option
              v-for="device in devices"
              :key="device.machineId"
              :label="`${device.name} (${device.mall} ${device.floor})`"
              :value="device.machineId"
            />
          </el-select>
        </el-form-item>

        <!-- 操作按鈕 -->
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="generateReport">
            <i class="el-icon-search" /> 生成車牌搜索報表
          </el-button>
          <el-button @click="resetSearch">
            <i class="el-icon-refresh" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 統計摘要 -->
    <div v-if="summaryData.totalSearches > 0" class="summary-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">總搜索次數</div>
            <div class="summary-value">{{ summaryData.totalSearches.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">成功搜索次數</div>
            <div class="summary-value success">{{ summaryData.successSearches.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">失敗搜索次數</div>
            <div class="summary-value error">{{ summaryData.failedSearches.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">成功率</div>
            <div class="summary-value">{{ summaryData.successRate }}%</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 設備分布餅圖 -->
    <div v-if="chartData.length > 0" class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>設備搜索分布</span>
          <div class="chart-actions">
            <el-dropdown @command="handleExport">
              <el-button type="text">
                <i class="el-icon-download" /> 導出
                <i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">PNG圖片</el-dropdown-item>
                <el-dropdown-item command="jpeg">JPEG圖片</el-dropdown-item>
                
                <el-dropdown-item command="xlsx">Excel文件</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="chart-wrapper">
          <div ref="deviceChart" v-loading="loading" class="device-chart" />
          <div v-if="!loading && chartData.length === 0" class="no-data">
            <i class="el-icon-info" />
            <p>暫無數據，請選擇時間範圍後點擊查詢</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 詳細數據表格 -->
    <div v-if="tableData.length > 0" class="data-container">
      <el-card>
        <div slot="header">
          <span>車牌搜索詳細記錄</span>
        </div>
        <el-table v-loading="loading" :data="tableData" border stripe max-height="600">
          <el-table-column type="index" label="序號" width="80" fixed="left" />
          <el-table-column prop="searchTime" label="搜索時間" width="180" sortable />
          <el-table-column prop="licensePlate" label="車牌號碼" width="120" />
          <el-table-column prop="deviceInfo" label="設備信息" width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="搜索結果" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="carParkInfo" label="停車信息" min-width="300" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-if="scope.row.carParkInfo">
                <div><strong>停車場:</strong> {{ scope.row.carParkInfo.carPark }}</div>
                <div><strong>車位:</strong> {{ scope.row.carParkInfo.bayNo }}</div>
                <div><strong>樓層:</strong> {{ scope.row.carParkInfo.floor }}</div>
                <div><strong>入場時間:</strong> {{ scope.row.carParkInfo.entryTime }}</div>
                <div><strong>攝像頭ID:</strong> {{ scope.row.carParkInfo.camID }}</div>
              </div>
              <span v-else class="no-info">無記錄</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'ReportSearchLPN',
  data() {
    return {
      baseURL: 'http://localhost:1338',
      apiName: 'statistical',

      form: {},

      // 查詢條件
      filterType: 'daterange',
      queryYear: '',
      queryMonth: '',
      queryDateRange: [],
      selectedDevice: '',
      devices: [],

      // 數據
      rawData: [],
      tableData: [],
      chartData: [],
      summaryData: {
        totalSearches: 0,
        successSearches: 0,
        failedSearches: 0,
        successRate: 0
      },

      // 狀態
      loading: false,
      chart: null,

      // 查詢控制
      maxRetries: 3,
      retryDelay: 2000
    }
  },
  created() {
    this.baseURL = this.$store.getters.baseUrl
    this.getDevices()
  },
  mounted() {
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage)
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    handleMessage(event) {
      // 處理來自iframe的消息
    },

    // 獲取設備列表
    async getDevices() {
      try {
        const response = await fetch('https://yohomall.esc-map.com/api/machines')
        const data = await response.json()
        this.devices = data.data || []
        console.log('獲取到設備列表:', this.devices.length, '個設備')
      } catch (error) {
        console.error('獲取設備列表失敗:', error)
        this.$message.error('獲取設備列表失敗')
        this.devices = []
      }
    },

    // 篩選類型變化處理
    handleFilterTypeChange() {
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []
    },

    // 獲取時間範圍
    getTimeRange() {
      let startDate, endDate

      if (this.filterType === 'year' && this.queryYear) {
        startDate = `${this.queryYear}-01-01T00:00:00.000Z`
        endDate = `${parseInt(this.queryYear) + 1}-01-01T00:00:00.000Z`
      } else if (this.filterType === 'month' && this.queryMonth) {
        const [year, month] = this.queryMonth.split('-')
        startDate = `${year}-${month}-01T00:00:00.000Z`
        let nextMonth = parseInt(month) + 1
        let nextYear = parseInt(year)
        if (nextMonth > 12) {
          nextMonth = 1
          nextYear += 1
        }
        endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
      } else if (this.filterType === 'daterange' && this.queryDateRange && this.queryDateRange.length === 2) {
        startDate = `${this.queryDateRange[0]}T00:00:00.000Z`
        endDate = `${this.queryDateRange[1]}T23:59:59.999Z`
      } else {
        // 默認最近30天
        const now = new Date()
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        startDate = thirtyDaysAgo.toISOString()
        endDate = now.toISOString()
      }

      return { startDate, endDate }
    },

    // 獲取車牌搜索數據
    async getSearchLPNData(retryCount = 0) {
      try {
        const timeRange = this.getTimeRange()
        const { startDate, endDate } = timeRange

        const params = {
          page: 1,
          pageSize: 10000,
          'filters[$and][0][event][$eq]': 'search_LPN',
          'filters[$and][1][createdAt][$gte]': startDate,
          'filters[$and][2][createdAt][$lte]': endDate
        }

        // 如果選擇了特定設備，添加設備篩選
        if (this.selectedDevice) {
          params['filters[$and][3][fid][$eq]'] = this.selectedDevice
        }

        console.log('車牌搜索查詢參數:', params)

        const firstResponse = await getData(this.apiName, params)
        console.log('API響應結構:', firstResponse)

        let allResults = firstResponse.results || []

        // 如果有更多頁面，繼續獲取
        if (firstResponse.pagination && firstResponse.pagination.pageCount > 1) {
          const totalPages = Math.min(firstResponse.pagination.pageCount, 10)
          console.log(`總共 ${firstResponse.pagination.pageCount} 頁，將獲取前 ${totalPages} 頁`)

          for (let page = 2; page <= totalPages; page++) {
            try {
              const pageParams = { ...params, page }
              const pageResponse = await getData(this.apiName, pageParams)
              if (pageResponse.results && pageResponse.results.length > 0) {
                allResults = allResults.concat(pageResponse.results)
              }
              await this.delay(200)
            } catch (error) {
              console.error(`獲取第 ${page} 頁數據失敗:`, error)
              break
            }
          }
        }

        console.log('總共獲取到車牌搜索數據:', allResults.length, '條記錄')
        return allResults
      } catch (error) {
        console.error(`獲取車牌搜索數據失敗 (嘗試 ${retryCount + 1}/${this.maxRetries + 1}):`, error)

        if (retryCount < this.maxRetries && this.shouldRetry(error)) {
          console.log(`等待 ${this.retryDelay}ms 後重試...`)
          await this.delay(this.retryDelay)
          return this.getSearchLPNData(retryCount + 1)
        }

        return []
      }
    },

    // 解析車牌信息
    parseCarParkInfo(nameStr) {
      if (!nameStr || nameStr === 'No record found') {
        return null
      }

      try {
        // 嘗試解析類似 "{lPN='KY8885', carPark='YM1', bayNo='D2-174', entryTime='2025-06-16 22:54:36', floor='L2', camID='1405201'}" 的格式
        const cleanStr = nameStr.replace(/[{}]/g, '').trim()
        const pairs = cleanStr.split(', ')
        const result = {}

        pairs.forEach(pair => {
          const [key, value] = pair.split('=')
          if (key && value) {
            result[key.trim()] = value.replace(/'/g, '').trim()
          }
        })

        return {
          licensePlate: result.lPN || '',
          carPark: result.carPark || '',
          bayNo: result.bayNo || '',
          entryTime: result.entryTime || '',
          floor: result.floor || '',
          camID: result.camID || ''
        }
      } catch (error) {
        console.error('解析車牌信息失敗:', error, nameStr)
        return null
      }
    },

    // 從 event_target 提取車牌號
    extractLicensePlate(eventTarget) {
      if (!eventTarget) return ''
      // 從 "search_LPN_text_KY8885" 中提取 "KY8885"
      const match = eventTarget.match(/search_LPN_text_(.+)/)
      return match ? match[1] : ''
    },

    // 處理車牌搜索數據
    processSearchData(rawData) {
      console.log('開始處理車牌搜索數據，原始數據量:', rawData.length)

      const tableData = []
      const deviceStats = new Map()
      let successCount = 0
      let failedCount = 0

      rawData.forEach(record => {
        // 解析車牌信息
        const carParkInfo = this.parseCarParkInfo(record.name)
        const licensePlate = this.extractLicensePlate(record.event_target)
        const isSuccess = carParkInfo !== null
        const deviceInfo = this.getDeviceName(record.fid)

        // 統計成功/失敗次數
        if (isSuccess) {
          successCount++
        } else {
          failedCount++
        }

        // 統計設備分布
        if (deviceStats.has(record.fid)) {
          deviceStats.set(record.fid, deviceStats.get(record.fid) + 1)
        } else {
          deviceStats.set(record.fid, 1)
        }

        // 構建表格數據
        tableData.push({
          id: record.id,
          searchTime: this.formatDateTime(record.createdAt),
          licensePlate: licensePlate,
          deviceInfo: deviceInfo,
          status: isSuccess ? '成功' : '失敗',
          carParkInfo: carParkInfo,
          fid: record.fid
        })
      })

      // 按時間倒序排列
      tableData.sort((a, b) => new Date(b.searchTime) - new Date(a.searchTime))

      // 構建設備分布圖表數據
      const chartData = Array.from(deviceStats.entries()).map(([fid, count]) => ({
        name: this.getDeviceName(fid),
        value: count,
        fid: fid
      })).sort((a, b) => b.value - a.value)

      // 計算統計摘要
      const totalSearches = rawData.length
      const successRate = totalSearches > 0 ? Math.round((successCount / totalSearches) * 100) : 0

      return {
        tableData,
        chartData,
        summaryData: {
          totalSearches,
          successSearches: successCount,
          failedSearches: failedCount,
          successRate
        }
      }
    },

    // 獲取設備名稱
    getDeviceName(deviceId) {
      const device = this.devices.find(d => d.machineId === deviceId)
      return device ? `${device.name} (${device.mall} ${device.floor})` : `設備${deviceId}`
    },

    // 格式化日期時間
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 判斷是否應該重試
    shouldRetry(error) {
      if (error.response && error.response.status === 503) return true
      if (error.response && error.response.status === 502) return true
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) return true
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) return true
      return false
    },

    // 延遲函數
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 生成車牌搜索報表
    async generateReport() {
      this.loading = true

      try {
        const timeText = this.getTimeRangeText()
        const deviceText = this.selectedDevice
          ? `設備 ${this.getDeviceName(this.selectedDevice)} 的` : '所有設備的'

        this.$message.info(`正在獲取 ${timeText} ${deviceText}車牌搜索數據...`)

        // 獲取車牌搜索數據
        const rawData = await this.getSearchLPNData()

        if (rawData.length === 0) {
          this.$message.warning('未獲取到任何車牌搜索數據，請檢查查詢條件')
          this.tableData = []
          this.chartData = []
          this.summaryData = {
            totalSearches: 0,
            successSearches: 0,
            failedSearches: 0,
            successRate: 0
          }
          return
        }

        this.$message.info(`成功獲取到 ${rawData.length} 條車牌搜索記錄，正在處理數據...`)

        // 處理數據
        const processedData = this.processSearchData(rawData)
        this.rawData = rawData
        this.tableData = processedData.tableData
        this.chartData = processedData.chartData
        this.summaryData = processedData.summaryData

        // 生成圖表
        this.$nextTick(() => {
          this.initDeviceChart()
        })

        this.$message.success(`成功生成車牌搜索報表！總計 ${rawData.length} 條記錄`)
      } catch (error) {
        console.error('生成車牌搜索報表失敗:', error)
        this.$message.error('生成車牌搜索報表失敗')
      } finally {
        this.loading = false
      }
    },

    // 獲取時間範圍文本
    getTimeRangeText() {
      if (this.filterType === 'year' && this.queryYear) {
        return `${this.queryYear}年`
      } else if (this.filterType === 'month' && this.queryMonth) {
        const [year, month] = this.queryMonth.split('-')
        return `${year}年${parseInt(month)}月`
      } else if (this.filterType === 'daterange' && this.queryDateRange && this.queryDateRange.length === 2) {
        return `${this.queryDateRange[0]} 至 ${this.queryDateRange[1]}`
      } else {
        return '最近30天'
      }
    },

    // 初始化設備分布餅圖
    initDeviceChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.deviceChart, 'macarons')

      const option = {
        title: {
          text: '設備搜索分布',
          subtext: `總搜索次數：${this.summaryData.totalSearches.toLocaleString()}`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>搜索次數：${params.value.toLocaleString()}<br/>占比：${params.percent}%`
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          data: this.chartData.map(item => item.name)
        },
        series: [
          {
            name: '搜索次數',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chartData
          }
        ]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 重置搜尋
    resetSearch() {
      this.filterType = 'daterange'
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []
      this.selectedDevice = ''

      this.rawData = []
      this.tableData = []
      this.chartData = []
      this.summaryData = {
        totalSearches: 0,
        successSearches: 0,
        failedSearches: 0,
        successRate: 0
      }

      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `車牌搜索設備分布_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },

    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `車牌搜索設備分布_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.tableData || this.tableData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      try {
        import('xlsx').then(XLSX => {
          const headers = ['序號', '搜索時間', '車牌號碼', '設備信息', '搜索結果', '停車場', '車位', '樓層', '入場時間', '攝像頭ID']
          const ws_data = [
            headers,
            ...this.tableData.map((item, index) => [
              index + 1,
              item.searchTime,
              item.licensePlate,
              item.deviceInfo,
              item.status,
              item.carParkInfo ? item.carParkInfo.carPark : '',
              item.carParkInfo ? item.carParkInfo.bayNo : '',
              item.carParkInfo ? item.carParkInfo.floor : '',
              item.carParkInfo ? item.carParkInfo.entryTime : '',
              item.carParkInfo ? item.carParkInfo.camID : ''
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '車牌搜索記錄')

          // 設置列寬
          ws['!cols'] = [
            { wch: 8 }, // 序號
            { wch: 20 }, // 搜索時間
            { wch: 12 }, // 車牌號碼
            { wch: 25 }, // 設備信息
            { wch: 10 }, // 搜索結果
            { wch: 12 }, // 停車場
            { wch: 15 }, // 車位
            { wch: 8 }, // 樓層
            { wch: 20 }, // 入場時間
            { wch: 15 } // 攝像頭ID
          ]

          XLSX.writeFile(wb, `車牌搜索記錄_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch(() => {
          this.exportCSV()
        })
      } catch (error) {
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const headers = ['序號', '搜索時間', '車牌號碼', '設備信息', '搜索結果', '停車場', '車位', '樓層', '入場時間', '攝像頭ID']
      const csvContent = [
        headers.join(','),
        ...this.tableData.map((item, index) => [
          index + 1,
          `"${item.searchTime}"`,
          `"${item.licensePlate}"`,
          `"${item.deviceInfo}"`,
          `"${item.status}"`,
          `"${item.carParkInfo ? item.carParkInfo.carPark : ''}"`,
          `"${item.carParkInfo ? item.carParkInfo.bayNo : ''}"`,
          `"${item.carParkInfo ? item.carParkInfo.floor : ''}"`,
          `"${item.carParkInfo ? item.carParkInfo.entryTime : ''}"`,
          `"${item.carParkInfo ? item.carParkInfo.camID : ''}"`
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `車牌搜索記錄_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.summary-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;

  .summary-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;

    &.success {
      color: #67C23A;
    }

    &.error {
      color: #F56C6C;
    }
  }
}

.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-actions {
    .el-button {
      border: none;
      background: transparent;
      color: #606266;

      &:hover {
        color: #409EFF;
        background: rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 400px;
  position: relative;
}

.device-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.data-container {
  margin-top: 20px;
}

.no-info {
  color: #999;
  font-style: italic;
}
</style>
