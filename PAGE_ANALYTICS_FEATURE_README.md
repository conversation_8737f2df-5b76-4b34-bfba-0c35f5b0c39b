# 頁面訪問統計功能說明

## 功能概述

新建的頁面訪問統計功能 (`page-analytics.vue`) 提供了完整的頁面點擊統計分析，支持餅圖和柱狀圖兩種視覺化展示方式，並包含完整的導出功能。

## 核心功能

### 1. 頁面統計範圍
統計以下10個頁面的訪問數據：

| 中文名稱 | 英文標識 | 查詢參數 |
|---------|---------|---------|
| 主頁 | home | `&filters[$and][3][event_target][$contains]=home` |
| 美食 | food | `&filters[$and][3][event_target][$contains]=food` |
| 購物 | shop | `&filters[$and][3][event_target][$contains]=shop` |
| 戲院 | cinema | `&filters[$and][3][event_target][$contains]=cinema` |
| 精彩活動 | promotion | `&filters[$and][3][event_target][$contains]=promotion` |
| 主題設施 | focalpoint | `&filters[$and][3][event_target][$contains]=focalpoint` |
| 泊車 | carsearch | `&filters[$and][3][event_target][$contains]=carsearch` |
| 交通資訊 | transport | `&filters[$and][3][event_target][$contains]=transport` |
| 服務與設施 | services | `&filters[$and][3][event_target][$contains]=services` |
| 周邊遊 | nearby | `&filters[$and][3][event_target][$contains]=nearby` |

### 2. 時間篩選功能
支持三種時間篩選方式：
- **年份篩選**: 選擇特定年份的數據
- **月份篩選**: 選擇特定月份的數據  
- **日期範圍篩選**: 選擇自定義日期範圍

### 3. 圖表展示方式
- **餅圖**: 直觀顯示各頁面訪問量佔比
- **柱狀圖**: 清晰對比各頁面訪問量差異

### 4. 數據表格
提供詳細的數據表格，包含：
- 序號
- 頁面名稱（中文）
- 英文標識
- 訪問次數
- 佔比百分比

## 技術實現

### API查詢邏輯

#### 1. 並發查詢
```javascript
// 並發獲取所有頁面的訪問統計
const promises = this.pageConfig.map(page => 
  this.getPageVisitCount(page.english)
)
const results = await Promise.all(promises)
```

#### 2. 查詢參數構建
```javascript
async getPageVisitCount(pageKey) {
  const params = {
    page: 1,
    pageSize: 1, // 只需要總數
    'filters[$and][2][event_target][$contains]': pageKey, // 頁面篩選
    ...this.getTimeFilterParams() // 時間篩選
  }
  
  const response = await getData(this.apiName, params)
  return {
    pageKey,
    total: response.pagination.total
  }
}
```

#### 3. 時間篩選參數
根據不同的篩選類型構建對應的時間參數：

**年份篩選**:
```javascript
timeParams['filters[$and][0][createdAt][$gte]'] = `${year}-01-01T00:00:00.000Z`
timeParams['filters[$and][1][createdAt][$lt]'] = `${year+1}-01-01T00:00:00.000Z`
```

**月份篩選**:
```javascript
timeParams['filters[$and][0][createdAt][$gte]'] = `${year}-${month}-01T00:00:00.000Z`
timeParams['filters[$and][1][createdAt][$lt]'] = `${nextYear}-${nextMonth}-01T00:00:00.000Z`
```

**日期範圍篩選**:
```javascript
timeParams['filters[$and][0][createdAt][$gte]'] = `${startDate}T00:00:00.000Z`
timeParams['filters[$and][1][createdAt][$lte]'] = `${endDate}T23:59:59.999Z`
```

### 數據處理

#### 1. 百分比計算
```javascript
const totalVisits = results.reduce((sum, result) => sum + result.total, 0)
const percentage = totalVisits > 0 ? ((result.total / totalVisits) * 100).toFixed(1) : 0
```

#### 2. 數據排序
```javascript
.sort((a, b) => b.count - a.count) // 按訪問數降序排列
```

### 圖表實現

#### 1. 餅圖配置
```javascript
const option = {
  title: {
    text: '頁面訪問統計',
    subtext: `總訪問數：${totalVisits.toLocaleString()}`,
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: function(params) {
      return `${params.name}<br/>訪問數：${params.value.toLocaleString()}<br/>佔比：${params.percent}%`
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: this.chartData.map(item => item.chinese)
  },
  series: [{
    name: '頁面訪問',
    type: 'pie',
    radius: '50%',
    data: this.chartData.map(item => ({
      value: item.count,
      name: item.chinese
    }))
  }]
}
```

#### 2. 柱狀圖配置
```javascript
const option = {
  title: {
    text: '頁面訪問統計',
    subtext: `共${this.chartData.length}個頁面，總訪問數：${totalVisits.toLocaleString()}`,
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    formatter: function(params) {
      const param = params[0]
      const dataItem = this.chartData[param.dataIndex]
      return `頁面：${param.name}<br/>訪問數：${param.value.toLocaleString()}<br/>佔比：${dataItem.percentage}%`
    }
  },
  xAxis: {
    type: 'category',
    data: nameList,
    axisLabel: {
      interval: 0,
      rotate: 45,
      fontSize: 10
    }
  },
  yAxis: {
    type: 'value',
    name: '訪問數'
  },
  series: [{
    name: '訪問數',
    type: 'bar',
    data: countList
  }]
}
```

## 導出功能

### 支持格式
- **PNG圖片**: 高質量點陣圖
- **JPEG圖片**: 壓縮圖片格式
- **PDF文檔**: 便攜式文檔格式
- **SVG向量圖**: 可縮放向量圖形
- **Excel文件**: 包含數據的電子表格

### 導出數據結構
Excel/CSV導出包含以下欄位：
- 頁面名稱
- 英文標識
- 訪問次數
- 佔比(%)

## 用戶界面

### 1. 時間篩選區域
- 篩選類型選擇器
- 對應的日期選擇器
- 生成統計按鈕
- 重置按鈕

### 2. 圖表類型切換
- 餅圖/柱狀圖單選按鈕組
- 實時切換圖表類型

### 3. 圖表展示區域
- 圖表標題顯示當前類型
- 導出按鈕位於右上角
- 響應式圖表大小

### 4. 數據表格
- 完整的數據列表
- 格式化的數字顯示
- 排序功能

## 使用流程

### 基本使用
1. **選擇時間範圍**: 選擇篩選類型並設置時間範圍
2. **生成統計**: 點擊"生成頁面統計"按鈕
3. **查看結果**: 系統並發查詢所有頁面數據
4. **切換視圖**: 在餅圖和柱狀圖之間切換
5. **導出數據**: 選擇需要的格式進行導出

### 高級功能
- **實時切換**: 圖表類型可以實時切換無需重新查詢
- **數據排序**: 自動按訪問量降序排列
- **百分比計算**: 自動計算各頁面佔比
- **格式化顯示**: 大數字自動格式化（如1.2k）

## 性能優化

### 1. 並發查詢
使用 `Promise.all()` 並發查詢所有頁面數據，提高查詢效率。

### 2. 按需加載
導出功能的外部庫（jsPDF、xlsx）採用動態導入，減少初始加載時間。

### 3. 數據緩存
圖表類型切換時復用已查詢的數據，無需重新請求API。

### 4. 響應式設計
圖表自動適應窗口大小變化。

## 錯誤處理

### 1. API錯誤
- 單個頁面查詢失敗時返回0，不影響其他頁面
- 顯示友好的錯誤提示信息

### 2. 數據驗證
- 檢查時間範圍的有效性
- 驗證圖表數據的完整性

### 3. 導出錯誤
- 外部庫不可用時自動降級
- 提供備用導出方案

## 瀏覽器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 文件結構

```
src/views/report/page-analytics.vue
├── Template (模板)
│   ├── 時間篩選表單
│   ├── 圖表類型切換
│   ├── 圖表展示區域
│   └── 數據表格
├── Script (邏輯)
│   ├── 數據查詢方法
│   ├── 圖表初始化方法
│   ├── 導出功能方法
│   └── 工具方法
└── Style (樣式)
    ├── 圖表容器樣式
    ├── 按鈕樣式
    └── 響應式佈局
```

## 注意事項

1. **API依賴**: 需要確保 `getData` API 正常工作
2. **時間格式**: 使用ISO 8601格式進行時間查詢
3. **數據量**: 大數據量時可能需要增加loading提示
4. **權限控制**: 可根據需要添加頁面訪問權限控制

## 擴展建議

1. **更多頁面**: 可以輕鬆添加新的頁面到 `pageConfig` 數組
2. **更多圖表**: 可以添加折線圖、面積圖等其他圖表類型
3. **實時更新**: 可以添加定時刷新功能
4. **數據鑽取**: 可以添加點擊頁面查看詳細數據的功能
