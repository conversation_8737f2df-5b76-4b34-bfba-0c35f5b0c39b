# 503 Service Unavailable 錯誤解決方案

## 問題分析

您遇到的 503 Service Unavailable 錯誤是典型的服務器過載問題，主要原因：

### 🚨 **503錯誤的常見原因**
1. **API請求過於頻繁** - Strapi服務器無法處理大量並發請求
2. **服務器資源不足** - CPU、內存或數據庫連接池耗盡
3. **請求速率限制** - Strapi內建的速率限制機制被觸發
4. **數據庫查詢超時** - 複雜的過濾查詢導致數據庫響應緩慢

### 📊 **您的API調用模式分析**
```
URL: https://yohomall.esc-map.com/content-manager/collection-types/api::statistical.statistical/
參數: ?page=1&pageSize=1&filters[$and][0][createdAt][$gte]=2025-05-01T00:00:00.000Z&filters[$and][1][createdAt][$lte]=2025-06-01T00:00:00.000Z&filters[$and][2][event_target][$contains]=services
```

這個查詢涉及：
- 時間範圍過濾（1個月的數據）
- 事件目標過濾（包含特定頁面）
- 只獲取總數（pageSize=1）

## 解決方案實施

### 🛡️ **1. 三層查詢模式**

#### **超穩定模式（推薦用於503錯誤）**
```javascript
// 間隔1秒，最保守的方式
async ultraStableQuery(tasks) {
  const delayMs = 1000 // 1秒間隔
  
  for (let i = 0; i < tasks.length; i++) {
    const result = await this.getPageData(tasks[i].pageKey, tasks[i].date)
    results.push(result)
    
    // 每次請求後等待1秒
    if (i < tasks.length - 1) {
      await this.delay(delayMs)
    }
  }
}
```

#### **穩定模式**
```javascript
// 間隔500ms，平衡速度和穩定性
async sequentialQuery(tasks) {
  const delayMs = 500 // 500ms間隔
  // 順序執行，適中的延遲
}
```

#### **快速模式**
```javascript
// 每批2個請求，批次間隔1秒
async concurrentQuery(tasks) {
  const batchSize = 2 // 減少批次大小
  const batchDelay = 1000 // 增加批次間延遲
}
```

### 🔄 **2. 重試機制**

```javascript
async getPageData(pageKey, date, retryCount = 0) {
  try {
    const response = await getData(this.apiName, params)
    return response.pagination.total
  } catch (error) {
    // 檢查是否為503錯誤
    if (retryCount < this.maxRetries && this.shouldRetry(error)) {
      console.log(`等待 ${this.retryDelay}ms 後重試...`)
      await this.delay(this.retryDelay)
      return this.getPageData(pageKey, date, retryCount + 1)
    }
    return 0
  }
}

// 判斷是否應該重試
shouldRetry(error) {
  // 503 Service Unavailable
  if (error.response && error.response.status === 503) return true
  // 502 Bad Gateway
  if (error.response && error.response.status === 502) return true
  // 網絡錯誤
  if (error.code === 'NETWORK_ERROR') return true
  // 超時錯誤
  if (error.code === 'ECONNABORTED') return true
  return false
}
```

### ⚙️ **3. 配置參數優化**

```javascript
// 重試配置
maxRetries: 3,        // 最大重試3次
retryDelay: 2000,     // 重試前等待2秒

// 查詢間隔
ultraStable: 1000ms,  // 超穩定模式：1秒間隔
sequential: 500ms,    // 穩定模式：500ms間隔
concurrent: 1000ms,   // 快速模式：批次間1秒間隔
```

## 使用建議

### 📋 **場景選擇指南**

#### **🐌 超穩定模式 - 推薦用於503錯誤**
- ✅ **適用場景**: 經常遇到503錯誤
- ✅ **特點**: 1秒間隔，最保守
- ✅ **優點**: 幾乎不會觸發503錯誤
- ❌ **缺點**: 查詢時間最長

#### **⚖️ 穩定模式**
- ✅ **適用場景**: 偶爾遇到503錯誤
- ✅ **特點**: 500ms間隔，平衡性能
- ✅ **優點**: 速度和穩定性平衡
- ❌ **缺點**: 仍可能遇到503錯誤

#### **⚡ 快速模式**
- ✅ **適用場景**: 服務器負載低時
- ✅ **特點**: 小批量並發
- ✅ **優點**: 查詢速度快
- ❌ **缺點**: 容易觸發503錯誤

### 🕐 **時間估算**

假設查詢30天×3頁面 = 90次API調用：

| 模式 | 間隔 | 總時間 | 適用場景 |
|------|------|--------|----------|
| 超穩定 | 1秒 | ~90秒 | 頻繁503錯誤 |
| 穩定 | 500ms | ~45秒 | 偶爾503錯誤 |
| 快速 | 批次 | ~45秒 | 服務器正常 |

### 💡 **最佳實踐**

#### **1. 首次使用建議**
```
1. 選擇「超穩定模式」
2. 選擇較小的時間範圍（如7天）
3. 選擇較少的頁面（如3個核心頁面）
4. 觀察是否還有503錯誤
```

#### **2. 根據結果調整**
```
✅ 如果超穩定模式成功 → 可以嘗試穩定模式
✅ 如果穩定模式成功 → 可以嘗試快速模式
❌ 如果仍有503錯誤 → 聯繫服務器管理員
```

#### **3. 時間範圍建議**
```
📅 按天查詢：建議不超過30天
📅 按月查詢：建議不超過12個月
📊 頁面選擇：建議不超過5個頁面同時查詢
```

## 錯誤監控和反饋

### 📊 **進度顯示**
系統會實時顯示查詢進度：
```
查詢進度: 15/90 (超穩定模式 - 避免503錯誤)
```

### 🔍 **錯誤日誌**
所有錯誤都會記錄在控制台：
```javascript
console.error(`獲取頁面 services 在 2025-05-01 的數據失敗 (嘗試 1/4):`, error)
```

### 🎯 **成功率統計**
系統會統計成功率，幫助選擇最適合的模式。

## 服務器端建議

### 🔧 **Strapi配置優化**
如果您有服務器管理權限，可以考慮：

1. **增加速率限制閾值**
2. **優化數據庫索引**
3. **增加服務器資源**
4. **配置負載均衡**

### 📈 **監控指標**
建議監控：
- API響應時間
- 並發連接數
- 數據庫查詢時間
- 服務器資源使用率

## 總結

通過實施這些解決方案，您應該能夠：

1. **✅ 避免503錯誤** - 使用超穩定模式
2. **✅ 自動重試** - 遇到錯誤時自動重試
3. **✅ 進度監控** - 實時了解查詢進度
4. **✅ 靈活選擇** - 根據服務器狀況選擇模式

**推薦設置**：
- 查詢模式：超穩定模式
- 時間範圍：不超過30天
- 頁面數量：3-5個核心頁面
- 重試次數：3次
- 重試延遲：2秒

這樣配置應該能夠有效解決503錯誤問題！🎉
