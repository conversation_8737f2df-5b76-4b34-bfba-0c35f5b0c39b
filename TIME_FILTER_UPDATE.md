# 時間篩選限制更新說明

## 更新概述

根據您的需求，我已經在設備頁面訪問趨勢分析功能中增加了時間篩選的最前時間值限制，設置為**2025年5月1日**。

## 主要更新內容

### 🗓️ **時間限制設置**

#### **最早可選時間**
- **日期模式**: 2025年5月1日
- **月份模式**: 2025年5月

#### **實施範圍**
- ✅ 日期選擇器禁用早於2025-05-01的日期
- ✅ 月份選擇器禁用早於2025-05的月份
- ✅ 快捷選項自動調整到最早可選時間
- ✅ 默認時間範圍自動調整
- ✅ 界面提示用戶最早可選時間

### 🔧 **技術實現**

#### **1. 日期選擇器限制**
```javascript
dayPickerOptions: {
  // 設置最早可選日期為2025年5月1日
  disabledDate(time) {
    const minDate = new Date('2025-05-01')
    return time.getTime() < minDate.getTime()
  },
  shortcuts: [
    // 快捷選項會自動調整到最早可選時間
  ]
}
```

#### **2. 月份選擇器限制**
```javascript
monthPickerOptions: {
  // 設置最早可選月份為2025年5月
  disabledDate(time) {
    const minDate = new Date('2025-05-01')
    return time.getTime() < minDate.getTime()
  },
  shortcuts: [
    // 快捷選項會自動調整到最早可選時間
  ]
}
```

#### **3. 默認時間範圍調整**
```javascript
setDefaultTimeRange() {
  const end = new Date()
  const start = new Date()
  const minDate = new Date('2025-05-01') // 最早可選日期

  if (this.timeDimension === 'day') {
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30) // 最近30天
    
    // 確保開始日期不早於2025年5月1日
    if (start < minDate) {
      start.setTime(minDate.getTime())
    }
  }
  // 月份模式同樣處理
}
```

### 📋 **快捷選項更新**

#### **日期模式快捷選項**
- ✅ **最近一週**: 自動調整到2025-05-01（如果計算結果早於此日期）
- ✅ **最近一個月**: 自動調整到2025-05-01（如果計算結果早於此日期）
- ✅ **最近三個月**: 自動調整到2025-05-01（如果計算結果早於此日期）
- ➕ **從5月1日開始**: 新增快捷選項，直接選擇從2025-05-01到今天

#### **月份模式快捷選項**
- ✅ **最近6個月**: 自動調整到2025-05（如果計算結果早於此月份）
- ✅ **最近12個月**: 自動調整到2025-05（如果計算結果早於此月份）
- ➕ **從2025年5月開始**: 新增快捷選項，直接選擇從2025-05到當前月份

### 🎨 **用戶界面改進**

#### **視覺提示**
在時間選擇器下方添加了提示信息：
```
ℹ️ 最早可選日期：2025年5月1日    (日期模式)
ℹ️ 最早可選月份：2025年5月       (月份模式)
```

#### **動態提示**
提示文字會根據當前選擇的時間維度動態變化：
- 日期模式：顯示"最早可選日期：2025年5月1日"
- 月份模式：顯示"最早可選月份：2025年5月"

### 🔄 **自動調整邏輯**

#### **快捷選項智能調整**
當用戶點擊快捷選項時，系統會自動檢查計算出的開始時間：

```javascript
// 例如：最近一個月快捷選項
onClick(picker) {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
  
  // 確保開始日期不早於2025-05-01
  const minDate = new Date('2025-05-01')
  if (start < minDate) {
    start.setTime(minDate.getTime())
  }
  
  picker.$emit('pick', [start, end])
}
```

#### **默認時間範圍智能調整**
頁面加載時，默認時間範圍會自動調整：
- 如果"最近30天"的開始日期早於2025-05-01，則自動調整為從2025-05-01開始
- 如果"最近6個月"的開始月份早於2025-05，則自動調整為從2025-05開始

### 📊 **使用場景示例**

#### **場景1：當前日期為2025年4月15日**
- 用戶點擊"最近一個月" → 自動選擇：2025-05-01 至 2025-04-15
- 用戶手動選擇 → 無法選擇2025-05-01之前的日期

#### **場景2：當前日期為2025年6月15日**
- 用戶點擊"最近一個月" → 選擇：2025-05-15 至 2025-06-15
- 用戶點擊"從5月1日開始" → 選擇：2025-05-01 至 2025-06-15

#### **場景3：當前日期為2025年12月31日**
- 用戶點擊"最近12個月" → 選擇：2025-05 至 2025-12
- 用戶點擊"從2025年5月開始" → 選擇：2025-05 至 2025-12

### ⚠️ **注意事項**

#### **數據可用性**
- 設置此限制意味著系統假設2025年5月1日之前沒有有效數據
- 如果用戶嘗試查詢更早的數據，選擇器會自動阻止

#### **API查詢影響**
- 所有API查詢的時間範圍都會被限制在2025-05-01之後
- 這有助於避免查詢無效的歷史數據，提高查詢效率

#### **用戶體驗**
- 用戶無法選擇2025-05-01之前的日期，選擇器會顯示為禁用狀態
- 快捷選項會智能調整，確保用戶始終能選擇有效的時間範圍

### 🎯 **預期效果**

#### **數據查詢優化**
1. **避免無效查詢**: 防止查詢2025年5月1日之前的無效數據
2. **提高查詢效率**: 減少不必要的API調用
3. **數據一致性**: 確保所有查詢都在有效的數據範圍內

#### **用戶體驗提升**
1. **清晰的時間限制**: 用戶明確知道可查詢的時間範圍
2. **智能快捷選項**: 快捷選項自動調整到有效範圍
3. **視覺提示**: 界面清楚顯示最早可選時間

#### **系統穩定性**
1. **減少錯誤**: 避免查詢不存在的歷史數據導致的錯誤
2. **性能優化**: 限制查詢範圍有助於提高系統性能
3. **數據完整性**: 確保查詢結果的數據完整性和準確性

## 總結

通過設置2025年5月1日作為最早可選時間，系統現在能夠：

✅ **自動限制時間選擇範圍**
✅ **智能調整快捷選項**
✅ **提供清晰的用戶提示**
✅ **優化數據查詢效率**
✅ **提升用戶體驗**

這個更新確保了用戶只能查詢有效的數據範圍，同時保持了良好的用戶體驗和系統性能。
