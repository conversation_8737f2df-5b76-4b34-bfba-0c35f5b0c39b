<template>
  <div class="app-container">
    <h1>Report</h1>

    <!-- 時間篩選表單 -->
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="篩選類型">
        <el-select v-model="filterType" placeholder="請選擇篩選類型" @change="handleFilterTypeChange">
          <el-option label="年份" value="year" />
          <el-option label="月份" value="month" />
          <el-option label="日期範圍" value="daterange" />
        </el-select>
      </el-form-item>

      <!-- 年份篩選 -->
      <el-form-item v-if="filterType === 'year'" label="年份">
        <el-date-picker
          v-model="queryYear"
          type="year"
          placeholder="選擇年份"
          format="yyyy"
          value-format="yyyy"
        />
      </el-form-item>

      <!-- 月份篩選 -->
      <el-form-item v-if="filterType === 'month'" label="月份">
        <el-date-picker
          v-model="queryMonth"
          type="month"
          placeholder="選擇月份"
          format="yyyy-MM"
          value-format="yyyy-MM"
        />
      </el-form-item>

      <!-- 日期範圍篩選 -->
      <el-form-item v-if="filterType === 'daterange'" label="日期範圍">
        <el-date-picker
          v-model="queryDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="開始日期"
          end-placeholder="結束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <el-form-item label="">
        <el-input v-model="listQuery._q" placeholder="搜尋關鍵詞" />
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" @click="handleSearch()">搜尋</el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 圖表展示區域 -->
    <div class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>設備記錄統計</span>

          <el-button
            type="success"
            size="small"
            :loading="machineChartLoading"
            @click="generateMachineChart"
          >
            生成設備統計圖表
          </el-button>

        </div>
        <div v-if="chartData.length > 0" class="chart-wrapper">
          <div class="chart-export-btn">
            <el-dropdown @command="handleExport" trigger="click">
              <el-button type="text" size="small" icon="el-icon-download">
                導出 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">
                  <i class="el-icon-picture"></i> Download PNG image
                </el-dropdown-item>
                <el-dropdown-item command="jpeg">
                  <i class="el-icon-picture"></i> Download JPEG image
                </el-dropdown-item>
                <el-dropdown-item command="xlsx">
                  <i class="el-icon-s-grid"></i> Download XLS
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div ref="fidChart" class="fid-chart" />
        </div>
        <div v-else class="no-data">
          <p>暫無數據，請先搜尋數據後生成圖表</p>
          <p style="font-size: 12px; color: #666;">
            調試信息: list長度={{ list.length }}, chartData長度={{ chartData.length }}
          </p>
        </div>
      </el-card>
    </div>


  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import Pagination from '@/components/Pagination'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'Report',
  components: { Pagination },
  filters: {
    getArea: function(area) {
      return String(area).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: 'statistical', // 變量修改，請求主題名稱
      listLoading: false,
      btnLoading: false,

      modelId: '',
      // 彈框
      dialogFormVisible: false,
      // 地圖彈框
      dialogMapVisible: false,
      // 彈框屬於新增還是修改
      dialogStatus: '',
      dialogVisible: false,
      textMap: {
        update: '修改',
        create: '新增'
      },
      form: {},
      formRow: {},

      // 時間篩選相關
      filterType: 'daterange', // 篩選類型：year, month, daterange
      queryYear: '', // 年份篩選
      queryMonth: '', // 月份篩選
      queryDateRange: [], // 日期範圍篩選

      // 圖表相關
      chart: null, // echarts實例
      chartData: [], // 圖表數據
      chartLoading: false, // 圖表加載狀態
      machineChartLoading: false, // 設備圖表加載狀態
      machines: [], // 設備列表

      // 列表請求參數
      listQuery: {
        page: 1,
        pageSize: 1000,
        // newshop: true,
        sort: 'createdAt:DESC',
        _q: ''
      },
      statusOptions: [],

      resetTrigger: 0 // 控制重置邏輯的狀態變量 ImageUploader
      // 變量修改，表單驗證規則
    }
  },
  created() {
    // this.getList(this.listQuery)
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  mounted() {
    // 監聽來自 iframe 的消息
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    // 移除消息監聽
    window.removeEventListener('message', this.handleMessage)
    // 銷毀圖表實例
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    /*
    獲取列表數據
    */
    getList(p) {
      let params = p
      if (!p) {
        params = ''
      }
      this.listLoading = true
      getData(this.apiName, params).then(response => {
        const res = response.results
        this.list = res
        console.log(this.list)
        this.total = response.pagination.total
        this.listLoading = false
      })
    },

    // 時間轉換為ISO格式（參考項目中已有實現）
    timeToTimestamp(time) {
      const timestamp = Date.parse(new Date(time).toString())
      const d = new Date(timestamp)
      return d.toISOString()
    },

    // 格式化日期顯示
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 篩選類型變化處理
    handleFilterTypeChange() {
      // 清空之前的篩選條件
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []
      // 清除listQuery中的時間篩選參數
      this.clearTimeFilters()
    },

    // 清除時間篩選參數
    clearTimeFilters() {
      const keysToDelete = Object.keys(this.listQuery).filter(key =>
        key.includes('filters[$and]') && key.includes('createdAt')
      )
      keysToDelete.forEach(key => {
        delete this.listQuery[key]
      })
    },

    // 構建年份篩選參數
    buildYearFilter(year) {
      const startDate = `${year}-01-01T00:00:00.000Z`
      const endDate = `${parseInt(year) + 1}-01-01T00:00:00.000Z`

      this.listQuery['filters[$and][0][createdAt][$gte]'] = startDate
      this.listQuery['filters[$and][1][createdAt][$lt]'] = endDate
    },

    // 構建月份篩選參數
    buildMonthFilter(monthStr) {
      const [year, month] = monthStr.split('-')
      const startDate = `${year}-${month}-01T00:00:00.000Z`

      // 計算下個月的第一天
      let nextMonth = parseInt(month) + 1
      let nextYear = parseInt(year)
      if (nextMonth > 12) {
        nextMonth = 1
        nextYear += 1
      }
      const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`

      this.listQuery['filters[$and][0][createdAt][$gte]'] = startDate
      this.listQuery['filters[$and][1][createdAt][$lt]'] = endDate
    },

    // 構建日期範圍篩選參數
    buildDateRangeFilter(dateRange) {
      const startDate = `${dateRange[0]}T00:00:00.000Z`
      const endDate = `${dateRange[1]}T23:59:59.999Z`

      this.listQuery['filters[$and][0][createdAt][$gte]'] = startDate
      this.listQuery['filters[$and][1][createdAt][$lte]'] = endDate
    },

    // 搜尋
    async handleSearch() {
      this.listQuery.page = 1

      // 清除之前的時間篩選參數
      this.clearTimeFilters()

      // 根據篩選類型構建相應的篩選參數
      if (this.filterType === 'year' && this.queryYear) {
        this.buildYearFilter(this.queryYear)
      } else if (this.filterType === 'month' && this.queryMonth) {
        this.buildMonthFilter(this.queryMonth)
      } else if (this.filterType === 'daterange' && this.queryDateRange && this.queryDateRange.length === 2) {
        this.buildDateRangeFilter(this.queryDateRange)
      }

      console.log('搜尋參數:', this.listQuery)
      await this.getList(this.listQuery)
      this.generateMachineChart()
    },

    // 重置
    resetSearch() {
      this.listQuery = {
        page: 1,
        pageSize: 50,
        sort: 'createdAt:DESC',
        _q: ''
      }

      // 重置時間篩選條件
      this.filterType = 'daterange'
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []

      this.getList(this.listQuery)
    },

    // 生成圖表
    generateChart() {
      this.getList(this.listQuery)
      console.log('生成圖表 - list長度:', this.list.length)
      console.log('生成圖表 - list數據:', this.list)

      if (this.list.length === 0) {
        this.$message.warning('請先搜尋數據')
        return
      }

      this.chartLoading = true

      // 處理數據，按fid分組統計
      const fidStats = this.processFidData(this.list)
      console.log('處理後的fid統計數據:', fidStats)

      this.chartData = fidStats
      console.log('設置chartData後:', this.chartData)

      // 等待DOM更新後初始化圖表
      this.$nextTick(() => {
        this.initChart(fidStats)
        this.chartLoading = false
      })
    },

    // 處理fid數據統計
    processFidData(data) {
      const fidMap = new Map()

      // 統計每個fid的記錄數
      data.forEach(item => {
        const fid = item.fid || '未知設備'
        if (fidMap.has(fid)) {
          fidMap.set(fid, fidMap.get(fid) + 1)
        } else {
          fidMap.set(fid, 1)
        }
      })

      // 轉換為數組並排序
      const result = Array.from(fidMap.entries())
        .map(([fid, count]) => ({ fid, count }))
        .sort((a, b) => b.count - a.count) // 按記錄數降序排列

      return result
    },

    // 初始化圖表
    initChart(data) {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.fidChart, 'macarons')

      const fidList = data.map(item => `設備${item.fid}`)
      const countList = data.map(item => item.count)

      const option = {
        title: {
          text: '設備記錄統計',
          subtext: `共${data.length}個設備，總記錄數：${countList.reduce((sum, count) => sum + count, 0)}`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            return `${param.name}<br/>記錄數：${param.value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: fidList,
          axisLabel: {
            interval: 0,
            rotate: 45 // 旋轉標籤避免重疊
          }
        },
        yAxis: {
          type: 'value',
          name: '記錄數'
        },
        series: [
          {
            name: '記錄數',
            type: 'bar',
            data: countList,
            itemStyle: {
              color: function(params) {
                // 根據數值大小設置不同顏色
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
                return colors[params.dataIndex % colors.length]
              }
            },
            label: {
              show: true,
              position: 'top'
            }
          }
        ]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 獲取設備列表
    async getMachines() {
      try {
        const response = await fetch('https://yohomall.esc-map.com/api/machines')
        const data = await response.json()
        this.machines = data.data || []
        console.log('獲取到設備列表:', this.machines)
        return this.machines
      } catch (error) {
        console.error('獲取設備列表失敗:', error)
        this.$message.error('獲取設備列表失敗')
        return []
      }
    },

    // 獲取單個設備的記錄總數
    async getMachineRecordCount(machineId) {
      try {
        // 構建查詢參數，包含時間篩選和fid篩選
        const params = {
          page: 1,
          pageSize: 1, // 只需要獲取總數，不需要具體數據
          'filters[$and][2][fid][$eq]': machineId, // 使用正確的fid篩選語法
          ...this.getTimeFilterParams() // 添加時間篩選參數
        }

        const response = await getData(this.apiName, params)
        return {
          machineId,
          total: response.pagination.total
        }
      } catch (error) {
        console.error(`獲取設備 ${machineId} 記錄數失敗:`, error)
        return {
          machineId,
          total: 0
        }
      }
    },

    // 獲取當前時間篩選參數
    getTimeFilterParams() {
      const timeParams = {}

      // 根據篩選類型構建相應的篩選參數
      if (this.filterType === 'year' && this.queryYear) {
        const startDate = `${this.queryYear}-01-01T00:00:00.000Z`
        const endDate = `${parseInt(this.queryYear) + 1}-01-01T00:00:00.000Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lt]'] = endDate
      } else if (this.filterType === 'month' && this.queryMonth) {
        const [year, month] = this.queryMonth.split('-')
        const startDate = `${year}-${month}-01T00:00:00.000Z`
        let nextMonth = parseInt(month) + 1
        let nextYear = parseInt(year)
        if (nextMonth > 12) {
          nextMonth = 1
          nextYear += 1
        }
        const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lt]'] = endDate
      } else if (this.filterType === 'daterange' && this.queryDateRange && this.queryDateRange.length === 2) {
        const startDate = `${this.queryDateRange[0]}T00:00:00.000Z`
        const endDate = `${this.queryDateRange[1]}T23:59:59.999Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lte]'] = endDate
      }

      return timeParams
    },

    // 生成設備統計圖表
    async generateMachineChart() {
      this.machineChartLoading = true

      try {
        // 1. 獲取設備列表
        const machines = this.machines.length > 0 ? this.machines : await this.getMachines()

        if (machines.length === 0) {
          this.$message.warning('沒有找到設備數據')
          return
        }

        // 2. 並發獲取每個設備的記錄總數
        this.$message.info(`正在查詢 ${machines.length} 個設備的記錄數...`)

        const promises = machines.map(machine =>
          this.getMachineRecordCount(machine.machineId)
        )

        const results = await Promise.all(promises)
        console.log('設備記錄統計結果:', results)

        // 3. 處理數據，過濾掉沒有記錄的設備
        const chartData = results
          .filter(result => result.total > 0) // 只顯示有記錄的設備
          .map(result => {
            const machine = machines.find(m => m.machineId === result.machineId)
            return {
              fid: result.machineId,
              name: machine ? machine.name : `設備${result.machineId}`,
              mall: machine ? machine.mall : '',
              floor: machine ? machine.floor : '',
              count: result.total
            }
          })
          .sort((a, b) => b.count - a.count) // 按記錄數降序排列

        if (chartData.length === 0) {
          this.$message.warning('所有設備在當前時間範圍內都沒有記錄')
          return
        }

        // 4. 更新圖表數據並生成圖表
        this.chartData = chartData

        this.$nextTick(() => {
          this.initMachineChart(chartData)
        })

        this.$message.success(`成功生成 ${chartData.length} 個設備的統計圖表`)
      } catch (error) {
        console.error('生成設備統計圖表失敗:', error)
        this.$message.error('生成設備統計圖表失敗')
      } finally {
        this.machineChartLoading = false
      }
    },

    // 初始化設備圖表
    initMachineChart(data) {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.fidChart, 'macarons')

      const nameList = data.map(item => `${item.name}\n(${item.mall} ${item.floor})`)
      const countList = data.map(item => item.count)
      const totalRecords = countList.reduce((sum, count) => sum + count, 0)

      const option = {
        title: {
          text: '設備記錄統計',
          subtext: `共${data.length}個活躍設備，總記錄數：${totalRecords.toLocaleString()}`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            const dataItem = data[param.dataIndex]
            return `設備名稱：${dataItem.name}<br/>` +
                   `設備ID：${dataItem.fid}<br/>` +
                   `商場：${dataItem.mall}<br/>` +
                   `樓層：${dataItem.floor}<br/>` +
                   `記錄數：${param.value.toLocaleString()}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%', // 增加底部空間
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            interval: 0,
            rotate: 45, // 旋轉標籤避免重疊
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          name: '記錄數',
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'k'
              }
              return value
            }
          }
        },
        series: [
          {
            name: '記錄數',
            type: 'bar',
            data: countList,
            itemStyle: {
              color: function(params) {
                // 根據數值大小設置不同顏色
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
                return colors[params.dataIndex % colors.length]
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                if (params.value >= 1000) {
                  return (params.value / 1000).toFixed(1) + 'k'
                }
                return params.value
              }
            }
          }
        ]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'pdf':
            this.exportImageAsPDF()
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `設備記錄統計_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },

    // 備用PDF導出方法
    exportImageAsPDF() {
      const url = this.chart.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `設備記錄統計_${this.getCurrentDateString()}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.info('已導出PNG圖片，您可以使用打印功能轉換為PDF')
    },

    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `設備記錄統計_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.chartData || this.chartData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      // 嘗試使用xlsx庫導出Excel
      try {
        import('xlsx').then(XLSX => {
          const ws_data = [
            ['設備ID', '設備名稱', '商場', '樓層', '記錄數'], // 表頭
            ...this.chartData.map(item => [
              item.fid,
              item.name,
              item.mall || '',
              item.floor || '',
              item.count
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '設備記錄統計')

          // 設置列寬
          ws['!cols'] = [
            { wch: 10 }, // 設備ID
            { wch: 20 }, // 設備名稱
            { wch: 15 }, // 商場
            { wch: 10 }, // 樓層
            { wch: 12 }  // 記錄數
          ]

          XLSX.writeFile(wb, `設備記錄統計_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch((error) => {
          console.warn('xlsx庫未安裝:', error)
          this.exportCSV()
        })
      } catch (error) {
        console.warn('動態導入xlsx失敗:', error)
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const csvContent = [
        ['設備ID', '設備名稱', '商場', '樓層', '記錄數'].join(','),
        ...this.chartData.map(item => [
          item.fid,
          `"${item.name}"`,
          `"${item.mall || ''}"`,
          `"${item.floor || ''}"`,
          item.count
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `設備記錄統計_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 2022;
  background-color: #00000060;
}

.describe {
  display: inline-block;
  width: 350px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
}

.fileName {
  display: inline-block;
  margin-left: 12px;
}

.describe-tooltip {
  width: 420px;
}

.avatar {
  width: 120px;
}

.newicon {
  width: 30%;
  position: absolute;
  top: 0;
  left: 0;
}

.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.chart-wrapper {
  width: 100%;
  height: 400px;
  position: relative;
}

.chart-export-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;

  .el-button {
    border: none;
    background: transparent;
    color: #606266;

    &:hover {
      color: #409EFF;
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

.fid-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.data-container {
  margin-top: 20px;
}
</style>
