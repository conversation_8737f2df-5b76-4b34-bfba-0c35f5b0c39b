import request from '@/utils/request'

// 通用方法
// 獲取数据
export function getData(baseUrl, params) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/`,
    method: 'get',
    params
  })
}

// 獲取数据
export function getDataOne(baseUrl, params, num) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/` + num,
    method: 'get',
    params
  })
}

// 新增、修改、删除数据
export function postData(baseUrl, type, num, data) {
  return request({
    url: `/content-manager/collection-types/api::${baseUrl}.${baseUrl}/` + num,
    method: type,
    data
  })
}
// 新增、修改、删除数据 單條模式
export function postSingleData(baseUrl, type, num, data) {
  return request({
    url: `/content-manager/single-types/api::${baseUrl}.${baseUrl}/` + num,
    method: type,
    data
  })
}


// 上传文件
export function uploadData(data) {
  return request({
    url: `/upload`,
    method: "POST",
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
