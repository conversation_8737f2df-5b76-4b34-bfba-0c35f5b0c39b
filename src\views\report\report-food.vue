<template>
  <div class="app-container">
    <!-- 查詢條件 -->
    <el-card>
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <!-- 月份選擇 -->
        <el-form-item label="查詢月份">
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="選擇月份"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            :picker-options="monthPickerOptions"
          />
        </el-form-item>

        <!-- 設備選擇 -->
        <el-form-item label="選擇設備">
          <el-select
            v-model="selectedDevice"
            placeholder="選擇設備（默認全部）"
            clearable
            filterable
            style="width: 200px;"
          >
            <el-option label="全部設備" value="" />
            <el-option
              v-for="device in devices"
              :key="device.machineId"
              :label="`${device.name} (${device.mall} ${device.floor})`"
              :value="device.machineId"
            />
          </el-select>
        </el-form-item>


        <!-- 操作按鈕 -->
        <el-form-item>
          <el-button type="primary" @click="generateTrendChart" :loading="chartLoading">
            <i class="el-icon-search"></i> 生成店鋪訪問排行榜
          </el-button>
          <el-button @click="resetSearch">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 統計摘要 -->
    <div v-if="chartData.length > 0" class="summary-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">總訪問次數</div>
            <div class="summary-value">{{ totalVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">店鋪總數</div>
            <div class="summary-value">{{ totalShops.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">最熱門店鋪訪問量</div>
            <div class="summary-value">{{ maxShopVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-item">
            <div class="summary-label">平均每店訪問量</div>
            <div class="summary-value">{{ averageShopVisits.toLocaleString() }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 圖表展示 -->
    <div v-if="chartData.length > 0" class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>店鋪詳情頁訪問排行榜</span>
          <div class="chart-actions">
            <el-dropdown @command="handleExport">
              <el-button type="text">
                <i class="el-icon-download"></i> 導出
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">PNG圖片</el-dropdown-item>
                <el-dropdown-item command="jpeg">JPEG圖片</el-dropdown-item>
                
                <el-dropdown-item command="xlsx">Excel文件</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="chart-wrapper">
          <div v-loading="chartLoading" ref="trendChart" class="trend-chart"></div>
          <div v-if="!chartLoading && chartData.length === 0" class="no-data">
            <i class="el-icon-info"></i>
            <p>暫無數據，請選擇時間範圍後點擊查詢</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 數據表格 -->
    <div v-if="tableData.length > 0" class="data-container">
      <el-card>
        <div slot="header">
          <span>詳細數據</span>
        </div>
        <el-table v-loading="chartLoading" :data="tableData" border stripe max-height="400">
          <el-table-column type="index" label="排名" width="80" fixed="left">
            <template slot-scope="scope">
              <span style="font-weight: bold; color: #409EFF;">{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="shopName" label="店鋪名稱" min-width="200" show-overflow-tooltip />
          <el-table-column prop="visitCount" label="訪問次數" width="120" sortable>
            <template slot-scope="scope">
              <span style="font-weight: bold; color: #409EFF;">{{ scope.row.visitCount.toLocaleString() }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'ReportShop',
  data() {
    return {
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: 'statistical', // API名稱

      form: {},

      // 查詢條件
      selectedMonth: '', // 選中的月份
      selectedDevice: '', // 選中的設備ID，空字符串表示全部設備
      devices: [], // 設備列表

      // 查詢控制
      maxRetries: 3, // 最大重試次數
      retryDelay: 2000, // 重試延遲（毫秒）

      // 圖表相關
      chart: null, // echarts實例
      chartData: [], // 圖表數據
      tableData: [], // 表格數據
      chartLoading: false, // 圖表加載狀態

      // 月份選擇器配置
      monthPickerOptions: {
        // 設置最早可選月份為2025年5月
        disabledDate(time) {
          const minDate = new Date('2025-06-01')
          return time.getTime() < minDate.getTime()
        },
        shortcuts: [{
          text: '2025年5月',
          onClick(picker) {
            picker.$emit('pick', new Date('2025-06-01'))
          }
        }, {
          text: '當前月份',
          onClick(picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '上個月',
          onClick(picker) {
            const lastMonth = new Date()
            lastMonth.setMonth(lastMonth.getMonth() - 1)
            // 確保不早於2025年5月
            const minDate = new Date('2025-06-01')
            if (lastMonth < minDate) {
              picker.$emit('pick', minDate)
            } else {
              picker.$emit('pick', lastMonth)
            }
          }
        }]
      }
    }
  },
  computed: {
    // 計算統計摘要
    totalVisits() {
      if (this.chartData.length === 0) return 0
      return this.chartData.reduce((sum, item) => sum + (item.visitCount || 0), 0)
    },

    totalShops() {
      return this.chartData.length
    },

    maxShopVisits() {
      if (this.chartData.length === 0) return 0
      return Math.max(...this.chartData.map(item => item.visitCount || 0))
    },

    averageShopVisits() {
      if (this.chartData.length === 0) return 0
      return Math.round(this.totalVisits / this.chartData.length)
    }
  },
  created() {
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl

    // 設置默認月份為當前月份
    this.setDefaultMonth()

    // 獲取設備列表
    this.getDevices()
  },
  mounted() {
    // 監聽來自 iframe 的消息
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    // 移除消息監聽
    window.removeEventListener('message', this.handleMessage)
    // 銷毀圖表實例
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 處理消息
    handleMessage(event) {
      // 處理來自iframe的消息
    },

    // 設置默認月份
    setDefaultMonth() {
      const currentDate = new Date()
      const minDate = new Date('2025-06-01')

      // 如果當前日期早於2025年5月，則設置為2025年5月
      if (currentDate < minDate) {
        this.selectedMonth = '2025-05'
      } else {
        this.selectedMonth = currentDate.toISOString().substr(0, 7)
      }
    },

    // 獲取設備列表
    async getDevices() {
      try {
        const response = await fetch('https://yohomall.esc-map.com/api/machines')
        const data = await response.json()
        this.devices = data.data || []
        console.log('獲取到設備列表:', this.devices.length, '個設備')
      } catch (error) {
        console.error('獲取設備列表失敗:', error)
        this.$message.error('獲取設備列表失敗')
        this.devices = []
      }
    },

    // 獲取選中月份的時間範圍
    getMonthTimeRange() {
      if (!this.selectedMonth) {
        this.$message.warning('請選擇查詢月份')
        return null
      }

      const [year, month] = this.selectedMonth.split('-')
      const startDate = `${year}-${month}-01T00:00:00.000Z`

      // 計算下個月的第一天作為結束時間
      let nextMonth = parseInt(month) + 1
      let nextYear = parseInt(year)
      if (nextMonth > 12) {
        nextMonth = 1
        nextYear += 1
      }
      const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`

      return { startDate, endDate }
    },

    // 一次性獲取店鋪詳情頁訪問數據
    async getAllShopData(retryCount = 0) {
      try {
        const timeRange = this.getMonthTimeRange()
        if (!timeRange) {
          return []
        }

        const { startDate, endDate } = timeRange

        const params = {
          page: 1,
          pageSize: 10000, // 一万条，如果还有则再请求，这样不会超过最大请求量
          'filters[$and][0][createdAt][$gte]': startDate,
          'filters[$and][1][createdAt][$lte]': endDate,
          'filters[$and][2][event][$eq]': 'food_detail' // 專門篩選店鋪詳情頁訪問
        }

        // 如果選擇了特定設備，添加設備篩選
        if (this.selectedDevice) {
          params['filters[$and][3][fid][$eq]'] = this.selectedDevice
        }

        console.log('店鋪詳情頁查詢參數:', params)

        // 獲取第一頁數據
        const firstResponse = await getData(this.apiName, params)
        console.log('API響應結構:', firstResponse)
        console.log('第一頁數據:', firstResponse.results?.length || 0, '條記錄')
        console.log('分頁信息:', firstResponse.pagination)

        let allResults = firstResponse.results || []

        // 如果有更多頁面，繼續獲取
        if (firstResponse.pagination && firstResponse.pagination.pageCount > 1) {
          const totalPages = Math.min(firstResponse.pagination.pageCount, 10) // 限制最多10頁，避免請求過多
          console.log(`總共 ${firstResponse.pagination.pageCount} 頁，將獲取前 ${totalPages} 頁`)

          for (let page = 2; page <= totalPages; page++) {
            try {
              const pageParams = { ...params, page }
              const pageResponse = await getData(this.apiName, pageParams)
              if (pageResponse.results && pageResponse.results.length > 0) {
                allResults = allResults.concat(pageResponse.results)
                console.log(`第 ${page} 頁獲取到 ${pageResponse.results.length} 條記錄`)
              }
              // 添加小延遲避免請求過快
              await this.delay(200)
            } catch (error) {
              console.error(`獲取第 ${page} 頁數據失敗:`, error)
              break
            }
          }
        }

        console.log('總共獲取到店鋪詳情頁數據:', allResults.length, '條記錄')
        return allResults
      } catch (error) {
        console.error(`獲取店鋪詳情頁數據失敗 (嘗試 ${retryCount + 1}/${this.maxRetries + 1}):`, error)

        // 檢查是否為503錯誤或網絡錯誤，且還有重試次數
        if (retryCount < this.maxRetries && this.shouldRetry(error)) {
          console.log(`等待 ${this.retryDelay}ms 後重試...`)
          await this.delay(this.retryDelay)
          return this.getAllShopData(retryCount + 1)
        }

        return []
      }
    },

    // 本地處理店鋪數據，按店鋪名稱分組統計
    processShopData(rawData) {
      console.log('開始處理店鋪數據，原始數據量:', rawData.length)

      const shopMap = new Map()
      let processedCount = 0
      let validRecords = 0

      // 處理原始數據，按店鋪名稱分組
      rawData.forEach(record => {
        processedCount++

        if (!record.name || !record.event || record.event !== 'food_detail') {
          return
        }

        const shopName = record.name.trim() // 去除前後空格

        if (shopMap.has(shopName)) {
          shopMap.set(shopName, shopMap.get(shopName) + 1)
        } else {
          shopMap.set(shopName, 1)
        }

        validRecords++

        if (validRecords <= 10) { // 打印前10條有效記錄
          console.log(`有效記錄 ${validRecords}:`, {
            shopName: shopName,
            event: record.event,
            fid: record.fid,
            createdAt: record.createdAt
          })
        }
      })

      console.log(`處理完成: 總記錄 ${processedCount} 條，有效記錄 ${validRecords} 條`)
      console.log(`發現 ${shopMap.size} 個不同的店鋪`)

      // 轉換為數組並按訪問次數排序（從多到少）
      const result = Array.from(shopMap.entries()).map(([shopName, visitCount]) => ({
        shopName: shopName,
        visitCount: visitCount
      })).sort((a, b) => b.visitCount - a.visitCount)

      console.log('店鋪訪問排行榜（前10名）:', result.slice(0, 10))

      return result
    },

    // 判斷是否應該重試
    shouldRetry(error) {
      // 503 Service Unavailable
      if (error.response && error.response.status === 503) {
        return true
      }
      // 502 Bad Gateway
      if (error.response && error.response.status === 502) {
        return true
      }
      // 網絡錯誤
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        return true
      }
      // 超時錯誤
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return true
      }
      return false
    },

    // 延遲函數，用於控制API調用頻率
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 生成店鋪訪問排行榜
    async generateTrendChart() {
      this.chartLoading = true

      try {
        const timeRange = this.getMonthTimeRange()
        if (!timeRange) {
          return
        }

        const deviceText = this.selectedDevice ?
          `設備 ${this.getDeviceName(this.selectedDevice)} 的` : '所有設備的'

        this.$message.info(`正在獲取 ${this.selectedMonth} ${deviceText}店鋪詳情頁訪問數據...`)

        // 獲取所有店鋪詳情頁訪問數據（支持分頁）
        const rawData = await this.getAllShopData()

        if (rawData.length === 0) {
          this.$message.warning('未獲取到任何店鋪詳情頁訪問數據，請檢查查詢條件')
          this.chartData = []
          this.tableData = []
          return
        }

        this.$message.info(`成功獲取到 ${rawData.length} 條店鋪詳情頁訪問記錄，正在按店鋪分組統計...`)

        // 本地處理數據，按店鋪名稱分組統計
        this.chartData = this.processShopData(rawData)

        // 生成表格數據
        this.tableData = [...this.chartData]

        this.$nextTick(() => {
          this.initTrendChart()
        })

        this.$message.success(`成功生成 ${this.chartData.length} 個店鋪的訪問排行榜！`)
      } catch (error) {
        console.error('生成店鋪訪問排行榜失敗:', error)
        this.$message.error('生成店鋪訪問排行榜失敗')
      } finally {
        this.chartLoading = false
      }
    },

    // 獲取設備名稱
    getDeviceName(deviceId) {
      const device = this.devices.find(d => d.machineId === deviceId)
      return device ? `${device.name} (${device.mall} ${device.floor})` : `設備${deviceId}`
    },

    // 初始化店鋪排行柱狀圖
    initTrendChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.trendChart, 'macarons')

      const topShops = this.chartData
      const shopNames = topShops.map(item => item.shopName)
      const visitCounts = topShops.map(item => item.visitCount)

      const option = {
        title: {
          text: `店鋪詳情頁訪問排行榜`,
          left: 'center',
          textStyle: {
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          formatter: function(params) {
            const param = params[0]
            return `<div style="font-weight: bold; margin-bottom: 5px;">${param.name}</div>
                    <div style="margin: 2px 0;">
                      ${param.marker}<span style="display: inline-block; width: 80px;">訪問次數:</span>
                      <span style="font-weight: bold; color: #409EFF;">${param.value.toLocaleString()}</span>
                    </div>`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '12%',
          containLabel: true
        },
        dataZoom: [{
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: topShops.length > 10 ? 50 : 100, // 如果店鋪數量超過10個，默認顯示50%
          bottom: '5%'
        }, {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: topShops.length > 10 ? 50 : 100
        }],
        xAxis: {
          type: 'category',
          data: shopNames,
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            lineStyle: {
              color: '#ccc'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            interval: 0,
            rotate: 45, // 旋轉45度避免重疊
            formatter: function(value) {
              // 如果店鋪名稱太長，截斷並添加省略號
              if (value.length > 12) {
                return value.substring(0, 12) + '...'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '訪問次數',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(0) + 'k'
              }
              return value
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
              type: 'solid'
            }
          }
        },
        series: [{
          name: '訪問次數',
          type: 'bar',
          data: visitCounts,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: '#409EFF'
              }, {
                offset: 1, color: '#66B3FF'
              }]
            },
            borderRadius: [4, 4, 0, 0] // 圓角頂部
          },
          label: {
            show: true,
            position: 'top',
            color: '#409EFF',
            fontSize: 11,
            fontWeight: 'bold',
            formatter: function(params) {
              if (params.value >= 1000) {
                return (params.value / 1000).toFixed(1) + 'k'
              }
              return params.value
            }
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#66B3FF'
                }, {
                  offset: 1, color: '#409EFF'
                }]
              }
            }
          }
        }]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 重置搜尋
    resetSearch() {
      // 重置月份選擇
      this.setDefaultMonth()

      // 重置設備選擇
      this.selectedDevice = ''

      // 清空圖表數據
      this.chartData = []
      this.tableData = []
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `店鋪訪問排行榜_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },

    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `店鋪訪問排行榜_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.tableData || this.tableData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      try {
        import('xlsx').then(XLSX => {
          const headers = ['排名', '店鋪名稱', '訪問次數']
          const ws_data = [
            headers,
            ...this.tableData.map((item, index) => [
              index + 1,
              item.shopName,
              item.visitCount || 0
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '店鋪訪問排行榜')

          // 設置列寬
          ws['!cols'] = [
            { wch: 8 },  // 排名
            { wch: 30 }, // 店鋪名稱
            { wch: 15 }  // 訪問次數
          ]

          XLSX.writeFile(wb, `店鋪訪問排行榜_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch(() => {
          this.exportCSV()
        })
      } catch (error) {
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const headers = ['排名', '店鋪名稱', '訪問次數']
      const csvContent = [
        headers.join(','),
        ...this.tableData.map((item, index) => [
          index + 1,
          `"${item.shopName}"`, // 用引號包圍店鋪名稱，避免逗號問題
          item.visitCount || 0
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `店鋪訪問排行榜_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-actions {
    .el-button {
      border: none;
      background: transparent;
      color: #606266;

      &:hover {
        color: #409EFF;
        background: rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 600px;
  position: relative;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.summary-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;

  .summary-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
  }
}

.data-container {
  margin-top: 20px;
}
</style>
