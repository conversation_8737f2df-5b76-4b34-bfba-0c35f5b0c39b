# 设备记录统计图表功能

## 功能概述

为Report页面新增了设备记录统计图表功能，可以根据不同的fid（设备ID）显示对应设备的记录总数，使用柱状图进行可视化展示。

## 功能特性

### 1. 数据统计
- **自动分组**: 根据fid字段自动对数据进行分组统计
- **记录计数**: 统计每个设备的记录总数
- **智能排序**: 按记录数量降序排列，便于识别活跃设备
- **未知设备处理**: 对于没有fid的记录，统一归类为"未知设备"

### 2. 图表展示
- **柱状图**: 使用ECharts柱状图清晰展示各设备记录数量
- **多彩配色**: 每个设备使用不同颜色，便于区分
- **数据标签**: 在柱子顶部显示具体数值
- **交互提示**: 鼠标悬停显示详细信息

### 3. 用户界面
- **卡片布局**: 使用Element UI卡片组件包装图表
- **操作按钮**: 提供"生成图表"按钮，支持加载状态
- **响应式设计**: 图表自适应窗口大小变化
- **无数据提示**: 当没有数据时显示友好提示

## 数据格式支持

支持以下格式的埋点数据：

```javascript
{
  "id": 604,
  "event": "navbar_page",
  "type": "view", 
  "name": "home",
  "event_target": "view_navbar_page_home",
  "fid": "14",
  "createdAt": "2025-04-30T12:57:06.697Z",
  "updatedAt": "2025-04-30T12:57:06.697Z",
  "createdBy": null,
  "updatedBy": null
}
```

## 使用方法

### 1. 数据准备
1. 打开Report页面
2. 使用时间筛选功能选择需要分析的时间范围
3. 点击"搜索"按钮获取数据

### 2. 生成图表
1. 确保已有搜索结果数据
2. 点击"生成图表"按钮
3. 系统自动处理数据并生成柱状图

### 3. 图表交互
- **查看详情**: 鼠标悬停在柱子上查看设备ID和记录数
- **缩放**: 图表支持鼠标滚轮缩放
- **响应式**: 窗口大小变化时图表自动调整

## 技术实现

### 核心方法

#### 1. generateChart()
```javascript
// 生成图表的主方法
generateChart() {
  if (this.list.length === 0) {
    this.$message.warning('请先搜索数据')
    return
  }
  
  this.chartLoading = true
  const fidStats = this.processFidData(this.list)
  this.chartData = fidStats
  
  this.$nextTick(() => {
    this.initChart(fidStats)
    this.chartLoading = false
  })
}
```

#### 2. processFidData(data)
```javascript
// 处理fid数据统计
processFidData(data) {
  const fidMap = new Map()
  
  // 统计每个fid的记录数
  data.forEach(item => {
    const fid = item.fid || '未知设备'
    if (fidMap.has(fid)) {
      fidMap.set(fid, fidMap.get(fid) + 1)
    } else {
      fidMap.set(fid, 1)
    }
  })
  
  // 转换为数组并排序
  return Array.from(fidMap.entries())
    .map(([fid, count]) => ({ fid, count }))
    .sort((a, b) => b.count - a.count)
}
```

#### 3. initChart(data)
```javascript
// 初始化ECharts图表
initChart(data) {
  this.chart = echarts.init(this.$refs.fidChart, 'macarons')
  
  const option = {
    title: {
      text: '设备记录统计',
      subtext: `共${data.length}个设备，总记录数：${totalCount}`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    xAxis: {
      type: 'category',
      data: fidList,
      axisLabel: { interval: 0, rotate: 45 }
    },
    yAxis: {
      type: 'value',
      name: '记录数'
    },
    series: [{
      name: '记录数',
      type: 'bar',
      data: countList,
      itemStyle: { color: dynamicColors },
      label: { show: true, position: 'top' }
    }]
  }
  
  this.chart.setOption(option)
}
```

### 数据结构

```javascript
data() {
  return {
    // 图表相关
    chart: null,           // echarts实例
    chartData: [],         // 图表数据
    chartLoading: false    // 图表加载状态
  }
}
```

## 图表配置

### 颜色方案
使用9种不同颜色循环显示：
- `#5470c6` (蓝色)
- `#91cc75` (绿色) 
- `#fac858` (黄色)
- `#ee6666` (红色)
- `#73c0de` (浅蓝)
- `#3ba272` (深绿)
- `#fc8452` (橙色)
- `#9a60b4` (紫色)
- `#ea7ccc` (粉色)

### 图表尺寸
- **宽度**: 100% (自适应容器)
- **高度**: 400px
- **标签旋转**: X轴标签旋转45度避免重叠

## 注意事项

1. **数据依赖**: 必须先搜索获取数据才能生成图表
2. **性能考虑**: 大量设备时建议使用时间筛选限制数据范围
3. **浏览器兼容**: 需要支持ES6的现代浏览器
4. **内存管理**: 组件销毁时自动清理图表实例

## 示例场景

### 场景1: 分析设备活跃度
1. 选择最近一周的数据
2. 生成图表查看各设备记录数
3. 识别最活跃和最不活跃的设备

### 场景2: 设备故障排查
1. 选择故障时间段的数据
2. 查看哪些设备记录异常
3. 结合具体事件类型进行分析

### 场景3: 设备使用统计
1. 按月份筛选数据
2. 生成月度设备使用统计图表
3. 为设备维护计划提供数据支持
