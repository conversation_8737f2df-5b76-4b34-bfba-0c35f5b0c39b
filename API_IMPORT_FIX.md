# API 導入錯誤修復說明

## 問題描述

在創建 `report-food.vue` 頁面時，出現了以下編譯錯誤：

```
Module not found: Error: Can't resolve '@/api/common' in 'C:\Users\<USER>\Desktop\project\yoho\cms\cms-admin\src\views\report'
```

## 錯誤原因

### 🚨 **根本原因**
在創建新頁面時，使用了錯誤的API導入路徑：

```javascript
// ❌ 錯誤的導入
import { getData } from '@/api/common'
```

### 🔍 **問題分析**
1. **文件不存在**: `@/api/common.js` 文件在項目中不存在
2. **路徑錯誤**: 正確的API文件路徑應該是 `@/api/requestData.js`
3. **參考錯誤**: 在創建新頁面時沒有正確參考現有頁面的導入方式

## 解決方案

### ✅ **修復方法**
將錯誤的導入語句修改為正確的路徑：

```javascript
// ✅ 正確的導入
import { getData } from '@/api/requestData'
```

### 📁 **項目API結構**
通過檢查項目結構發現，正確的API文件組織如下：

```
src/
├── api/
│   ├── requestData.js    ← 正確的API文件
│   ├── user.js
│   └── other-api-files.js
└── views/
    └── report/
        ├── device-trend.vue    ← 參考頁面（使用正確導入）
        └── report-food.vue     ← 新創建的頁面（需要修復）
```

### 🔧 **修復步驟**

#### **步驟1: 檢查現有頁面的導入方式**
```javascript
// 在 device-trend.vue 中查看正確的導入
import { getData } from '@/api/requestData'
```

#### **步驟2: 修復新頁面的導入**
```javascript
// 在 report-food.vue 中修復導入
- import { getData } from '@/api/common'
+ import { getData } from '@/api/requestData'
```

#### **步驟3: 驗證修復結果**
```bash
# 檢查編譯錯誤是否消失
npm run serve
# 或
yarn serve
```

## 預防措施

### 📋 **最佳實踐**

#### **1. 創建新頁面時的檢查清單**
- ✅ 檢查現有頁面的導入方式
- ✅ 確認API文件的實際路徑
- ✅ 使用相同的導入語句
- ✅ 測試編譯是否成功

#### **2. 參考現有代碼**
在創建新頁面時，始終參考項目中已有的、功能相似的頁面：

```javascript
// 參考 device-trend.vue 的導入方式
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons')
```

#### **3. 項目結構熟悉**
了解項目的API文件組織結構：

```
src/api/
├── requestData.js     ← 主要的數據請求API
├── user.js           ← 用戶相關API
├── auth.js           ← 認證相關API
└── ...               ← 其他業務API
```

### 🔍 **常見錯誤模式**

#### **錯誤1: 假設API文件名**
```javascript
// ❌ 常見錯誤 - 假設文件名
import { getData } from '@/api/common'
import { getData } from '@/api/data'
import { getData } from '@/api/api'
```

#### **錯誤2: 路徑拼寫錯誤**
```javascript
// ❌ 拼寫錯誤
import { getData } from '@/api/requestdata'  // 缺少大寫D
import { getData } from '@/api/request-data' // 使用連字符
```

#### **錯誤3: 相對路徑錯誤**
```javascript
// ❌ 相對路徑錯誤
import { getData } from '../api/requestData'     // 路徑層級錯誤
import { getData } from '../../api/requestData'  // 路徑層級錯誤
```

### ✅ **正確做法**

#### **1. 使用絕對路徑**
```javascript
// ✅ 使用 @ 別名的絕對路徑
import { getData } from '@/api/requestData'
```

#### **2. 檢查文件是否存在**
在IDE中使用 Ctrl+Click（或 Cmd+Click）檢查導入路徑是否正確。

#### **3. 參考現有代碼**
```javascript
// ✅ 參考 device-trend.vue 的完整導入
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons')
```

## 技術細節

### 📦 **Webpack 別名配置**
項目使用了 Webpack 的別名配置：

```javascript
// vue.config.js 或 webpack.config.js
resolve: {
  alias: {
    '@': path.resolve(__dirname, 'src')
  }
}
```

這意味著：
- `@/api/requestData` 解析為 `src/api/requestData.js`
- `@/views/report` 解析為 `src/views/report/`

### 🔧 **API 文件結構**
`@/api/requestData.js` 文件導出的函數：

```javascript
// requestData.js 的典型結構
export function getData(apiName, params) {
  // API 請求邏輯
  return axios.get(`/api/${apiName}`, { params })
}

export function postData(apiName, data) {
  // POST 請求邏輯
  return axios.post(`/api/${apiName}`, data)
}
```

### 📊 **使用方式**
在組件中正確使用 getData 函數：

```javascript
// 在 Vue 組件中使用
import { getData } from '@/api/requestData'

export default {
  methods: {
    async fetchData() {
      try {
        const response = await getData('statistical', {
          page: 1,
          pageSize: 25,
          'filters[$and][0][event][$eq]': 'food_detail'
        })
        return response.data
      } catch (error) {
        console.error('API 請求失敗:', error)
      }
    }
  }
}
```

## 總結

### 🎯 **關鍵要點**
1. **正確的導入路徑**: `@/api/requestData`
2. **參考現有代碼**: 查看 `device-trend.vue` 的導入方式
3. **驗證文件存在**: 確保導入的文件確實存在
4. **使用絕對路徑**: 使用 `@` 別名避免相對路徑錯誤

### 🚀 **修復結果**
修復後，`report-food.vue` 頁面可以正常編譯和運行，具備完整的店鋪詳情頁訪問趨勢分析功能。

### 💡 **經驗教訓**
在創建新頁面或組件時，始終：
1. 先檢查項目的現有結構
2. 參考相似功能的現有代碼
3. 確認所有導入路徑的正確性
4. 進行編譯測試驗證

這樣可以避免類似的導入錯誤，提高開發效率。
