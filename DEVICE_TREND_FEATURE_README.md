# 設備頁面訪問趨勢分析功能說明

## 功能概述

新建的設備頁面訪問趨勢分析功能 (`device-trend.vue`) 提供了完整的設備級別頁面訪問趨勢分析，支持按天/按月的時間維度，多設備多頁面的組合分析，並包含完整的導出功能。

## 核心功能

### 1. 時間維度分析
- **按天統計**: 精確到每日的訪問數據分析
- **按月統計**: 月度訪問趨勢分析
- **靈活時間範圍**: 支持自定義時間範圍選擇

### 2. 多維度篩選
- **設備篩選**: 支持多選設備進行對比分析
- **頁面篩選**: 支持多選頁面進行組合分析
- **時間範圍**: 靈活的時間範圍選擇

### 3. 趨勢圖展示
- **多線折線圖**: 每個設備-頁面組合一條趨勢線
- **交互式圖表**: 支持縮放、平移、圖例切換
- **數據標註**: 清晰的數據點標記和數值顯示

### 4. 統計摘要
- **總訪問次數**: 所有選中設備和頁面的總訪問量
- **平均日訪問量**: 按時間範圍計算的平均值
- **最高單日訪問**: 峰值訪問量統計
- **活躍設備數**: 有訪問記錄的設備數量

## 技術實現

### API查詢邏輯

#### 1. 多維度並發查詢
```javascript
// 構建所有查詢任務
const tasks = []
for (const deviceId of this.selectedDevices) {
  for (const date of dates) {
    for (const pageKey of this.selectedPages) {
      tasks.push({
        deviceId,
        date,
        pageKey,
        promise: this.getDevicePageData(deviceId, pageKey, date)
      })
    }
  }
}

// 並發執行所有查詢
const results = await Promise.all(tasks.map(task => task.promise))
```

#### 2. 查詢參數構建
```javascript
async getDevicePageData(deviceId, pageKey, date) {
  const params = {
    page: 1,
    pageSize: 1, // 只需要總數
    'filters[$and][0][createdAt][$gte]': startDate,
    'filters[$and][1][createdAt][$lte]': endDate,
    'filters[$and][2][fid][$eq]': deviceId,        // 設備篩選
    'filters[$and][3][event_target][$contains]': pageKey // 頁面篩選
  }
  
  const response = await getData(this.apiName, params)
  return response.pagination.total
}
```

#### 3. 時間範圍處理

**按天查詢**:
```javascript
if (this.timeDimension === 'day') {
  startDate = `${date}T00:00:00.000Z`
  endDate = `${date}T23:59:59.999Z`
}
```

**按月查詢**:
```javascript
else {
  const [year, month] = date.split('-')
  startDate = `${year}-${month}-01T00:00:00.000Z`
  
  // 計算下個月的第一天
  let nextMonth = parseInt(month) + 1
  let nextYear = parseInt(year)
  if (nextMonth > 12) {
    nextMonth = 1
    nextYear += 1
  }
  endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
}
```

### 數據處理

#### 1. 數據聚合
```javascript
// 處理結果數據
const dataMap = new Map()

tasks.forEach((task, index) => {
  const key = `${task.deviceId}_${task.date}`
  if (!dataMap.has(key)) {
    const device = this.devices.find(d => d.machineId === task.deviceId)
    dataMap.set(key, {
      deviceId: task.deviceId,
      deviceName: device ? device.name : `設備${task.deviceId}`,
      date: task.date,
      total: 0
    })
  }
  
  const item = dataMap.get(key)
  item[task.pageKey] = results[index]
  item.total += results[index]
})
```

#### 2. 數據排序
```javascript
// 轉換為數組並排序
this.chartData = Array.from(dataMap.values()).sort((a, b) => {
  if (a.date !== b.date) {
    return a.date.localeCompare(b.date)
  }
  return a.deviceId.localeCompare(b.deviceId)
})
```

### 圖表實現

#### 1. 多線系列生成
```javascript
// 為每個設備和頁面組合創建一條線
const series = []
const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
let colorIndex = 0

for (const deviceId of devices) {
  const device = this.devices.find(d => d.machineId === deviceId)
  const deviceName = device ? device.name : `設備${deviceId}`
  
  for (const pageKey of this.selectedPages) {
    const pageName = this.getPageChineseName(pageKey)
    
    const data = dates.map(date => {
      const item = this.chartData.find(d => d.deviceId === deviceId && d.date === date)
      return item ? (item[pageKey] || 0) : 0
    })
    
    series.push({
      name: `${deviceName} - ${pageName}`,
      type: 'line',
      data: data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: colors[colorIndex % colors.length]
      }
    })
    
    colorIndex++
  }
}
```

#### 2. 圖表配置
```javascript
const option = {
  title: {
    text: '設備頁面訪問趨勢',
    subtext: `${this.timeDimension === 'day' ? '按天' : '按月'}統計`,
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    type: 'scroll',
    orient: 'horizontal',
    top: 40,
    data: series.map(s => s.name)
  },
  toolbox: {
    feature: {
      saveAsImage: { title: '保存為圖片' },
      dataZoom: { title: { zoom: '區域縮放', back: '區域縮放還原' } },
      restore: { title: '還原' }
    }
  },
  dataZoom: [
    { type: 'inside', start: 0, end: 100 },
    { start: 0, end: 100 }
  ],
  series: series
}
```

## 用戶界面

### 1. 篩選控制區域
- **時間維度選擇**: 按天/按月切換
- **時間範圍選擇**: 對應的日期/月份範圍選擇器
- **設備多選**: 支持多選設備進行對比
- **頁面多選**: 支持多選頁面進行分析
- **快捷時間範圍**: 預設的常用時間範圍

### 2. 圖表展示區域
- **600px高度**: 充足的圖表展示空間
- **工具欄**: 內置縮放、保存、還原功能
- **圖例**: 可滾動的圖例，支持點擊切換
- **數據縮放**: 支持滑動縮放和區域縮放

### 3. 統計摘要卡片
- **四個關鍵指標**: 總訪問、平均日訪問、最高單日、活躍設備
- **響應式佈局**: 自適應不同屏幕尺寸
- **數值格式化**: 大數字自動格式化顯示

### 4. 詳細數據表格
- **完整數據**: 包含所有查詢結果的詳細數據
- **動態列**: 根據選中的頁面動態生成列
- **最大高度**: 400px最大高度，超出滾動
- **數據格式化**: 數字格式化顯示

## 導出功能

### 支持格式
- **PNG/JPEG圖片**: 高質量圖表圖片
- **PDF文檔**: 橫向佈局的PDF文檔
- **SVG向量圖**: 可縮放向量圖形
- **Excel/CSV文件**: 包含完整數據的電子表格

### 導出數據結構
Excel/CSV導出包含以下欄位：
- 日期
- 設備名稱
- 設備ID
- 各選中頁面的訪問數
- 總計

## 使用流程

### 基本使用
1. **選擇時間維度**: 按天或按月
2. **設置時間範圍**: 選擇分析的時間範圍
3. **選擇設備**: 多選要分析的設備
4. **選擇頁面**: 多選要分析的頁面
5. **生成趨勢圖**: 點擊生成按鈕開始分析
6. **查看結果**: 查看趨勢圖、統計摘要和詳細數據
7. **導出數據**: 選擇需要的格式進行導出

### 高級功能
- **圖表交互**: 使用工具欄進行縮放、平移操作
- **圖例控制**: 點擊圖例隱藏/顯示特定趨勢線
- **數據鑽取**: 通過表格查看詳細數據
- **快捷選擇**: 使用預設的時間範圍快捷選項

## 性能優化

### 1. 智能查詢
- **並發查詢**: 使用Promise.all並發執行所有API請求
- **查詢優化**: 只獲取pagination.total，不獲取具體記錄
- **錯誤容錯**: 單個查詢失敗不影響整體結果

### 2. 數據處理
- **內存優化**: 使用Map進行高效的數據聚合
- **計算緩存**: 統計摘要使用computed屬性緩存計算結果
- **按需渲染**: 表格使用虛擬滾動處理大量數據

### 3. 圖表性能
- **數據抽樣**: 大數據量時自動進行數據抽樣
- **漸進渲染**: ECharts的漸進渲染處理大數據集
- **內存管理**: 組件銷毀時正確清理圖表實例

## 錯誤處理

### 1. 數據驗證
- **時間範圍驗證**: 檢查時間範圍的有效性
- **設備選擇驗證**: 至少選擇一個設備
- **頁面選擇驗證**: 至少選擇一個頁面

### 2. API錯誤處理
- **網絡錯誤**: 友好的錯誤提示
- **數據異常**: 異常數據的容錯處理
- **超時處理**: 長時間查詢的超時提示

### 3. 用戶體驗
- **加載狀態**: 清晰的加載進度提示
- **錯誤恢復**: 提供重試機制
- **操作引導**: 友好的操作提示信息

## 擴展建議

### 1. 功能擴展
- **實時更新**: 添加定時刷新功能
- **預警機制**: 設置訪問量異常預警
- **對比分析**: 支持不同時間段的對比分析
- **預測分析**: 基於歷史數據的趨勢預測

### 2. 性能優化
- **數據緩存**: 添加查詢結果緩存機制
- **增量更新**: 支持增量數據更新
- **後台計算**: 將複雜計算移至後台處理

### 3. 用戶體驗
- **保存配置**: 保存用戶的篩選配置
- **分享功能**: 支持圖表分享和嵌入
- **移動適配**: 優化移動端的使用體驗

## 注意事項

1. **數據量控制**: 大時間範圍和多設備組合可能產生大量API請求
2. **性能監控**: 注意監控查詢性能，必要時進行優化
3. **內存管理**: 及時清理不需要的數據和圖表實例
4. **用戶引導**: 提供清晰的操作說明和最佳實踐建議

## 文件結構

```
src/views/report/device-trend.vue
├── Template (模板)
│   ├── 篩選表單區域
│   ├── 圖表展示區域
│   ├── 統計摘要區域
│   └── 數據表格區域
├── Script (邏輯)
│   ├── 數據查詢方法
│   ├── 圖表初始化方法
│   ├── 統計計算方法
│   ├── 導出功能方法
│   └── 工具方法
└── Style (樣式)
    ├── 圖表容器樣式
    ├── 統計卡片樣式
    └── 響應式佈局
```
