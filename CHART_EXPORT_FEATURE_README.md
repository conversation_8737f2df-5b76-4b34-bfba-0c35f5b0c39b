# 圖表導出功能實現

## 功能概述

為設備記錄統計圖表添加了完整的導出功能，支持多種格式的導出，包括圖片、文檔和數據文件格式。導出按鈕位於圖表右上角，提供下拉菜單選擇不同的導出格式。

## 支持的導出格式

### 1. 圖片格式
- **PNG圖片** - 高質量點陣圖，適合網頁展示和打印
- **JPEG圖片** - 壓縮圖片格式，文件較小

### 2. 向量圖格式
- **SVG向量圖** - 可縮放向量圖形，適合高質量打印和編輯

### 3. 文檔格式
- **PDF文檔** - 便攜式文檔格式，適合報告和存檔

### 4. 數據格式
- **Excel文件 (XLSX)** - 包含圖表數據的電子表格，便於數據分析

## 用戶界面

### 導出按鈕設計
- **位置**: 圖表右上角
- **樣式**: 半透明背景，懸停時高亮
- **觸發**: 點擊展開下拉菜單
- **圖標**: 每種格式都有對應的圖標

### 下拉菜單選項
```
📷 Download PNG image
📷 Download JPEG image  
📄 Download PDF document
🎨 Download SVG vector image
📊 Download XLS
```

## 技術實現

### 核心導出方法

#### 1. handleExport(format)
```javascript
// 主導出處理方法
handleExport(format) {
  if (!this.chart) {
    this.$message.warning('請先生成圖表')
    return
  }
  
  try {
    switch (format) {
      case 'png': this.exportImage('png'); break
      case 'jpeg': this.exportImage('jpeg'); break  
      case 'pdf': this.exportPDF(); break
      case 'svg': this.exportSVG(); break
      case 'xlsx': this.exportXLSX(); break
    }
  } catch (error) {
    this.$message.error('導出失敗，請重試')
  }
}
```

#### 2. exportImage(type)
```javascript
// 導出PNG/JPEG圖片
exportImage(type) {
  const url = this.chart.getDataURL({
    type: type,
    pixelRatio: 2,        // 高分辨率
    backgroundColor: '#fff' // 白色背景
  })
  
  // 創建下載鏈接
  const link = document.createElement('a')
  link.href = url
  link.download = `設備記錄統計_${this.getCurrentDateString()}.${type}`
  link.click()
}
```

#### 3. exportPDF()
```javascript
// 導出PDF文檔
exportPDF() {
  import('jspdf').then(({ default: jsPDF }) => {
    const url = this.chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const img = new Image()
    img.onload = () => {
      const pdf = new jsPDF({
        orientation: img.width > img.height ? 'landscape' : 'portrait',
        unit: 'px',
        format: [img.width, img.height]
      })
      
      pdf.addImage(url, 'PNG', 0, 0, img.width, img.height)
      pdf.save(`設備記錄統計_${this.getCurrentDateString()}.pdf`)
    }
    img.src = url
  })
}
```

#### 4. exportSVG()
```javascript
// 導出SVG向量圖
exportSVG() {
  const svgStr = this.chart.renderToSVGString()
  const blob = new Blob([svgStr], { type: 'image/svg+xml' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `設備記錄統計_${this.getCurrentDateString()}.svg`
  link.click()
  
  URL.revokeObjectURL(url)
}
```

#### 5. exportXLSX()
```javascript
// 導出Excel文件
exportXLSX() {
  import('xlsx').then(XLSX => {
    const ws_data = [
      ['設備ID', '設備名稱', '商場', '樓層', '記錄數'],
      ...this.chartData.map(item => [
        item.fid, item.name, item.mall || '', 
        item.floor || '', item.count
      ])
    ]
    
    const ws = XLSX.utils.aoa_to_sheet(ws_data)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '設備記錄統計')
    
    // 設置列寬
    ws['!cols'] = [
      { wch: 10 }, { wch: 20 }, { wch: 15 }, 
      { wch: 10 }, { wch: 12 }
    ]
    
    XLSX.writeFile(wb, `設備記錄統計_${this.getCurrentDateString()}.xlsx`)
  })
}
```

### 文件命名規則

所有導出的文件都使用統一的命名格式：
```
設備記錄統計_YYYYMMDD_HHMM.{擴展名}
```

例如：
- `設備記錄統計_20250101_1430.png`
- `設備記錄統計_20250101_1430.pdf`
- `設備記錄統計_20250101_1430.xlsx`

### 錯誤處理和備用方案

#### 1. PDF導出備用方案
如果jsPDF庫不可用，自動降級為PNG圖片導出：
```javascript
.catch(() => {
  this.exportImageAsPDF()  // 導出PNG並提示用戶手動轉換
})
```

#### 2. Excel導出備用方案
如果xlsx庫不可用，自動降級為CSV格式：
```javascript
.catch(() => {
  this.exportCSV()  // 導出CSV格式
})
```

#### 3. 用戶提示
- 成功導出：顯示成功消息
- 失敗處理：顯示錯誤消息和重試建議
- 無數據：提示用戶先生成圖表

## CSS樣式設計

### 導出按鈕樣式
```scss
.chart-export-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
  
  .el-button {
    border: none;
    background: transparent;
    color: #606266;
    
    &:hover {
      color: #409EFF;
      background: rgba(64, 158, 255, 0.1);
    }
  }
}
```

### 設計特點
- **半透明背景**: 不遮擋圖表內容
- **懸停效果**: 提供視覺反饋
- **高層級**: z-index確保按鈕始終可見
- **圓角設計**: 與整體UI風格一致

## 使用方法

### 基本使用流程
1. **生成圖表**: 先使用"生成設備統計圖表"功能
2. **選擇格式**: 點擊圖表右上角的"導出"按鈕
3. **選擇格式**: 從下拉菜單選擇需要的導出格式
4. **自動下載**: 文件自動下載到瀏覽器默認下載目錄

### 各格式適用場景

#### PNG圖片
- ✅ 網頁展示
- ✅ 演示文稿
- ✅ 高質量打印
- ✅ 社交媒體分享

#### JPEG圖片  
- ✅ 文件大小要求較小
- ✅ 電子郵件附件
- ✅ 快速分享

#### PDF文檔
- ✅ 正式報告
- ✅ 存檔保存
- ✅ 打印輸出
- ✅ 跨平台兼容

#### SVG向量圖
- ✅ 高質量縮放
- ✅ 圖形編輯
- ✅ 網頁嵌入
- ✅ 印刷設計

#### Excel文件
- ✅ 數據分析
- ✅ 進一步處理
- ✅ 數據備份
- ✅ 報表製作

## 瀏覽器兼容性

### 支持的瀏覽器
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 功能兼容性
- **Canvas API**: 所有現代瀏覽器支持
- **Blob API**: 用於文件下載
- **Dynamic Import**: ES2020特性，現代瀏覽器支持

## 依賴庫

### 可選依賴
- **jsPDF**: PDF導出功能（動態加載）
- **xlsx**: Excel導出功能（動態加載）

### 備用方案
如果外部庫不可用，系統會自動降級到基礎功能：
- PDF → PNG圖片
- Excel → CSV文件

## 性能考慮

### 優化措施
1. **動態加載**: 只在需要時加載外部庫
2. **高分辨率**: pixelRatio=2 確保圖片質量
3. **內存管理**: 及時釋放Blob URL
4. **錯誤處理**: 完善的異常捕獲

### 文件大小
- PNG: 通常 100-500KB
- JPEG: 通常 50-200KB  
- PDF: 通常 100-600KB
- SVG: 通常 10-50KB
- Excel: 通常 5-20KB

## 注意事項

1. **圖表狀態**: 必須先生成圖表才能導出
2. **瀏覽器設置**: 確保允許文件下載
3. **文件權限**: 確保有寫入下載目錄的權限
4. **網絡連接**: PDF和Excel功能需要加載外部庫
