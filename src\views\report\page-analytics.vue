<template>
  <div class="app-container">
    <h1>頁面訪問統計</h1>

    <!-- 時間篩選表單 -->
    <el-form ref="form" :model="form" label-width="80px" :inline="true" size="small">
      <el-form-item label="篩選類型">
        <el-select v-model="filterType" placeholder="請選擇篩選類型" @change="handleFilterTypeChange">
          <el-option label="年份" value="year" />
          <el-option label="月份" value="month" />
          <el-option label="日期範圍" value="daterange" />
        </el-select>
      </el-form-item>

      <!-- 年份篩選 -->
      <el-form-item v-if="filterType === 'year'" label="年份">
        <el-date-picker
          v-model="queryYear"
          type="year"
          placeholder="選擇年份"
          format="yyyy"
          value-format="yyyy"
        />
      </el-form-item>

      <!-- 月份篩選 -->
      <el-form-item v-if="filterType === 'month'" label="月份">
        <el-date-picker
          v-model="queryMonth"
          type="month"
          placeholder="選擇月份"
          format="yyyy-MM"
          value-format="yyyy-MM"
        />
      </el-form-item>

      <!-- 日期範圍篩選 -->
      <el-form-item v-if="filterType === 'daterange'" label="日期範圍">
        <el-date-picker
          v-model="queryDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="開始日期"
          end-placeholder="結束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          :loading="chartLoading"
          @click="generatePageAnalytics"
        >
          生成頁面統計
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 圖表類型切換 -->
    <div class="chart-type-selector">
      <el-radio-group v-model="chartType" @change="updateChart">
        <el-radio-button label="pie">餅圖</el-radio-button>
        <el-radio-button label="bar">柱狀圖</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 圖表展示區域 -->
    <div class="chart-container">
      <el-card class="chart-card">
        <div slot="header" class="chart-header">
          <span>頁面訪問統計 - {{ chartType === 'pie' ? '餅圖' : '柱狀圖' }}</span>
          <div class="chart-actions">
            <el-dropdown @command="handleExport" trigger="click">
              <el-button type="text" size="small" icon="el-icon-download">
                導出 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="png">
                  <i class="el-icon-picture"></i> Download PNG image
                </el-dropdown-item>
                <el-dropdown-item command="jpeg">
                  <i class="el-icon-picture"></i> Download JPEG image
                </el-dropdown-item>
             
                <el-dropdown-item command="xlsx">
                  <i class="el-icon-s-grid"></i> Download XLS
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div v-if="chartData.length > 0" class="chart-wrapper">
          <div ref="pageChart" class="page-chart" />
        </div>
        <div v-else class="no-data">
          <p>暫無數據，請先生成頁面統計</p>
          <p style="font-size: 12px; color: #666;">
            調試信息: chartData長度={{ chartData.length }}
          </p>
        </div>
      </el-card>
    </div>

   

  </div>
</template>

<script>
import { getData } from '@/api/requestData'
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme

export default {
  name: 'PageAnalytics',
  data() {
    return {
      // baseURL: 'https://novoland.esc-map.com',
      baseURL: 'http://localhost:1338',
      apiName: 'statistical', // 變量修改，請求主題名稱

      form: {},

      // 時間篩選相關
      filterType: 'daterange', // 篩選類型：year, month, daterange
      queryYear: '', // 年份篩選
      queryMonth: '', // 月份篩選
      queryDateRange: [], // 日期範圍篩選

      // 圖表相關
      chart: null, // echarts實例
      chartData: [], // 圖表數據
      chartLoading: false, // 圖表加載狀態
      chartType: 'pie', // 圖表類型：pie, bar

      // 頁面配置
      pageConfig: [
        { chinese: "主頁", english: "home" },
        { chinese: "美食", english: "food" },
        { chinese: "購物", english: "shop" },
        { chinese: "戲院", english: "cinema" },
        { chinese: "精彩活動", english: "promotion" },
        { chinese: "主題設施", english: "focalpoint" },
        { chinese: "泊車", english: "carsearch" },
        { chinese: "交通資訊", english: "transport" },
        { chinese: "服務與設施", english: "services" },
        { chinese: "周邊遊", english: "nearby" }
      ]
    }
  },
  created() {
    // 將網址鏈接取出來
    this.baseURL = this.$store.getters.baseUrl
  },
  mounted() {
    // 監聽來自 iframe 的消息
    window.addEventListener('message', this.handleMessage)
  },
  beforeDestroy() {
    // 移除消息監聽
    window.removeEventListener('message', this.handleMessage)
    // 銷毀圖表實例
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    // 處理消息
    handleMessage(event) {
      // 處理來自iframe的消息
    },

    // 篩選類型變化處理
    handleFilterTypeChange() {
      // 清空之前的篩選條件
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []
    },

    // 獲取當前時間篩選參數
    getTimeFilterParams() {
      const timeParams = {}

      // 根據篩選類型構建相應的篩選參數
      if (this.filterType === 'year' && this.queryYear) {
        const startDate = `${this.queryYear}-01-01T00:00:00.000Z`
        const endDate = `${parseInt(this.queryYear) + 1}-01-01T00:00:00.000Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lt]'] = endDate
      } else if (this.filterType === 'month' && this.queryMonth) {
        const [year, month] = this.queryMonth.split('-')
        const startDate = `${year}-${month}-01T00:00:00.000Z`
        let nextMonth = parseInt(month) + 1
        let nextYear = parseInt(year)
        if (nextMonth > 12) {
          nextMonth = 1
          nextYear += 1
        }
        const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lt]'] = endDate
      } else if (this.filterType === 'daterange' && this.queryDateRange && this.queryDateRange.length === 2) {
        const startDate = `${this.queryDateRange[0]}T00:00:00.000Z`
        const endDate = `${this.queryDateRange[1]}T23:59:59.999Z`
        timeParams['filters[$and][0][createdAt][$gte]'] = startDate
        timeParams['filters[$and][1][createdAt][$lte]'] = endDate
      }

      return timeParams
    },

    // 獲取單個頁面的訪問統計
    async getPageVisitCount(pageKey) {
      try {
        // 構建查詢參數，包含時間篩選和頁面篩選
        const params = {
          page: 1,
          pageSize: 1, // 只需要獲取總數，不需要具體數據
          'filters[$and][2][event_target][$contains]': pageKey, // 使用頁面英文標識篩選
          ...this.getTimeFilterParams() // 添加時間篩選參數
        }

        const response = await getData(this.apiName, params)
        return {
          pageKey,
          total: response.pagination.total
        }
      } catch (error) {
        console.error(`獲取頁面 ${pageKey} 訪問數失敗:`, error)
        return {
          pageKey,
          total: 0
        }
      }
    },

    // 生成頁面統計
    async generatePageAnalytics() {
      this.chartLoading = true

      try {
        // 並發獲取所有頁面的訪問統計
        this.$message.info(`正在查詢 ${this.pageConfig.length} 個頁面的訪問數據...`)

        const promises = this.pageConfig.map(page =>
          this.getPageVisitCount(page.english)
        )

        const results = await Promise.all(promises)
        console.log('頁面訪問統計結果:', results)

        // 處理數據，計算百分比
        const totalVisits = results.reduce((sum, result) => sum + result.total, 0)

        const chartData = results
          .map(result => {
            const pageInfo = this.pageConfig.find(p => p.english === result.pageKey)
            const percentage = totalVisits > 0 ? ((result.total / totalVisits) * 100).toFixed(1) : 0
            return {
              english: result.pageKey,
              chinese: pageInfo ? pageInfo.chinese : result.pageKey,
              count: result.total,
              percentage: parseFloat(percentage)
            }
          })
          .sort((a, b) => b.count - a.count) // 按訪問數降序排列

        this.chartData = chartData

        this.$nextTick(() => {
          this.updateChart()
        })

        this.$message.success(`成功生成 ${chartData.length} 個頁面的統計數據`)
      } catch (error) {
        console.error('生成頁面統計失敗:', error)
        this.$message.error('生成頁面統計失敗')
      } finally {
        this.chartLoading = false
      }
    },

    // 更新圖表
    updateChart() {
      if (this.chartData.length === 0) {
        return
      }

      if (this.chartType === 'pie') {
        this.initPieChart()
      } else {
        this.initBarChart()
      }
    },

    // 初始化餅圖
    initPieChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.pageChart, 'macarons')

      const totalVisits = this.chartData.reduce((sum, item) => sum + item.count, 0)

      const option = {
        title: {
          text: '頁面訪問統計',
          subtext: `總訪問數：${totalVisits.toLocaleString()}`,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>訪問數：${params.value.toLocaleString()}<br/>佔比：${params.percent}%`
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.chartData.map(item => item.chinese)
        },
        series: [
          {
            name: '頁面訪問',
            type: 'pie',
            radius: '50%',
            data: this.chartData.map(item => ({
              value: item.count,
              name: item.chinese
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 初始化柱狀圖
    initBarChart() {
      if (this.chart) {
        this.chart.dispose()
      }

      this.chart = echarts.init(this.$refs.pageChart, 'macarons')

      const nameList = this.chartData.map(item => item.chinese)
      const countList = this.chartData.map(item => item.count)
      const totalVisits = countList.reduce((sum, count) => sum + count, 0)

      const option = {
        title: {
          text: '頁面訪問統計',
          subtext: `共${this.chartData.length}個頁面，總訪問數：${totalVisits.toLocaleString()}`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const param = params[0]
            const dataItem = this.chartData[param.dataIndex]
            return `頁面：${param.name}<br/>訪問數：${param.value.toLocaleString()}<br/>佔比：${dataItem.percentage}%`
          }.bind(this)
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: nameList,
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          name: '訪問數',
          axisLabel: {
            formatter: function(value) {
              if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'k'
              }
              return value
            }
          }
        },
        series: [
          {
            name: '訪問數',
            type: 'bar',
            data: countList,
            itemStyle: {
              color: function(params) {
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
                return colors[params.dataIndex % colors.length]
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                if (params.value >= 1000) {
                  return (params.value / 1000).toFixed(1) + 'k'
                }
                return params.value
              }
            }
          }
        ]
      }

      this.chart.setOption(option)

      // 監聽窗口大小變化
      window.addEventListener('resize', this.handleResize)
    },

    // 處理窗口大小變化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 重置搜尋
    resetSearch() {
      // 重置時間篩選條件
      this.filterType = 'daterange'
      this.queryYear = ''
      this.queryMonth = ''
      this.queryDateRange = []

      // 清空圖表數據
      this.chartData = []
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    // 處理圖表導出
    handleExport(format) {
      if (!this.chart) {
        this.$message.warning('請先生成圖表')
        return
      }

      try {
        switch (format) {
          case 'png':
            this.exportImage('png')
            break
          case 'jpeg':
            this.exportImage('jpeg')
            break
          case 'pdf':
            this.exportPDF()
            break
          case 'svg':
            this.exportSVG()
            break
          case 'xlsx':
            this.exportXLSX()
            break
          default:
            this.$message.error('不支持的導出格式')
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗，請重試')
      }
    },

    // 導出圖片格式 (PNG/JPEG)
    exportImage(type) {
      const url = this.chart.getDataURL({
        type: type,
        pixelRatio: 2,
        backgroundColor: '#fff'
      })

      const link = document.createElement('a')
      link.href = url
      link.download = `頁面訪問統計_${this.getCurrentDateString()}.${type}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success(`${type.toUpperCase()}圖片導出成功`)
    },

    

    // 導出SVG
    exportSVG() {
      const svgStr = this.chart.renderToSVGString()
      const blob = new Blob([svgStr], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `頁面訪問統計_${this.getCurrentDateString()}.svg`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('SVG向量圖導出成功')
    },

    // 導出XLSX
    exportXLSX() {
      if (!this.chartData || this.chartData.length === 0) {
        this.$message.warning('沒有數據可導出')
        return
      }

      try {
        import('xlsx').then(XLSX => {
          const ws_data = [
            ['頁面名稱', '英文標識', '訪問次數', '佔比(%)'], // 表頭
            ...this.chartData.map(item => [
              item.chinese,
              item.english,
              item.count,
              item.percentage
            ])
          ]

          const ws = XLSX.utils.aoa_to_sheet(ws_data)
          const wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws, '頁面訪問統計')

          // 設置列寬
          ws['!cols'] = [
            { wch: 15 }, // 頁面名稱
            { wch: 15 }, // 英文標識
            { wch: 12 }, // 訪問次數
            { wch: 10 }  // 佔比
          ]

          XLSX.writeFile(wb, `頁面訪問統計_${this.getCurrentDateString()}.xlsx`)
          this.$message.success('Excel文件導出成功')
        }).catch(() => {
          this.exportCSV()
        })
      } catch (error) {
        this.exportCSV()
      }
    },

    // 備用CSV導出方法
    exportCSV() {
      const csvContent = [
        ['頁面名稱', '英文標識', '訪問次數', '佔比(%)'].join(','),
        ...this.chartData.map(item => [
          `"${item.chinese}"`,
          `"${item.english}"`,
          item.count,
          item.percentage
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = `頁面訪問統計_${this.getCurrentDateString()}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)
      this.$message.success('CSV文件導出成功')
    },

    // 獲取當前日期字符串
    getCurrentDateString() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-type-selector {
  margin: 20px 0;
  text-align: center;
}

.chart-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-actions {
    .el-button {
      border: none;
      background: transparent;
      color: #606266;

      &:hover {
        color: #409EFF;
        background: rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 500px;
  position: relative;
}

.page-chart {
  width: 100%;
  height: 100%;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #999;
}

.data-container {
  margin-top: 20px;
}
</style>
