# 店鋪詳情頁訪問趨勢報告頁面

## 頁面概述

`report-shop.vue` 是一個專門用於展示店鋪詳情頁訪問次數趨勢的報告頁面。該頁面基於現有的 `device-trend.vue` 頁面結構創建，專注於分析用戶對店鋪詳情頁的訪問行為。

## 核心功能

### 🎯 **專門統計**
- **目標事件**: `event = 'food_detail'`
- **統計範圍**: 店鋪詳情頁的所有訪問記錄
- **數據來源**: Strapi API (`statistical` 集合)

### 📊 **數據展示**
- **趨勢圖表**: 基於 ECharts 的專業折線圖
- **統計摘要**: 總訪問次數、平均訪問量、最高單日/月訪問
- **詳細表格**: 按日期展示具體訪問數據
- **多種導出**: PNG、JPEG、SVG、Excel 格式

### ⚡ **性能優化**
- **一次性查詢**: 避免多次API請求
- **本地數據處理**: 高效的前端數據聚合
- **智能重試**: 自動處理網絡錯誤和503錯誤

## API 查詢參數

### 🔍 **核心篩選條件**
```javascript
const params = {
  page: 1,
  pageSize: 25,
  'filters[$and][0][createdAt][$gte]': startDate,
  'filters[$and][1][createdAt][$lte]': endDate,
  'filters[$and][2][event][$eq]': 'food_detail' // 關鍵篩選條件
}
```

### 📅 **時間範圍處理**
- **按天統計**: `2025-05-01T00:00:00.000Z` 到 `2025-05-01T23:59:59.999Z`
- **按月統計**: `2025-05-01T00:00:00.000Z` 到 `2025-06-01T00:00:00.000Z`

## 頁面結構

### 🎛️ **查詢控制區域**
```vue
<!-- 時間維度選擇 -->
<el-select v-model="timeDimension">
  <el-option label="按天統計" value="day" />
  <el-option label="按月統計" value="month" />
</el-select>

<!-- 時間範圍選擇 -->
<el-date-picker
  v-model="dayRange"
  type="daterange"
  :picker-options="dayPickerOptions"
/>

<!-- 查詢優化說明 -->
<el-tag type="success">
  <i class="el-icon-lightning"></i> 一次性查詢優化
</el-tag>
```

### 📈 **統計摘要區域**
```vue
<el-row :gutter="20">
  <el-col :span="6">
    <el-card class="summary-item">
      <div class="summary-label">總訪問次數</div>
      <div class="summary-value">{{ totalVisits.toLocaleString() }}</div>
    </el-card>
  </el-col>
  <!-- 其他統計項目 -->
</el-row>
```

### 📊 **圖表展示區域**
```vue
<el-card class="chart-card">
  <div slot="header" class="chart-header">
    <span>店鋪詳情頁訪問趨勢圖</span>
    <el-dropdown @command="handleExport">
      <el-button type="text">
        <i class="el-icon-download"></i> 導出
      </el-button>
    </el-dropdown>
  </div>
  <div ref="trendChart" class="trend-chart"></div>
</el-card>
```

### 📋 **數據表格區域**
```vue
<el-table :data="tableData" border stripe>
  <el-table-column prop="date" label="日期" />
  <el-table-column prop="shopVisits" label="店鋪詳情頁訪問次數" />
</el-table>
```

## 數據處理流程

### 1️⃣ **數據獲取**
```javascript
async getAllShopData() {
  // 構建查詢參數
  const params = {
    'filters[$and][0][createdAt][$gte]': startDate,
    'filters[$and][1][createdAt][$lte]': endDate,
    'filters[$and][2][event][$eq]': 'food_detail'
  }
  
  // 一次性獲取所有數據
  const response = await getData(this.apiName, params)
  return response.data || []
}
```

### 2️⃣ **數據處理**
```javascript
processShopData(rawData) {
  const dataMap = new Map()
  
  // 初始化日期結構
  dates.forEach(date => {
    dataMap.set(date, {
      date: date,
      shopVisits: 0
    })
  })
  
  // 處理原始數據
  rawData.forEach(record => {
    if (record.event === 'food_detail') {
      const dateKey = this.extractDateKey(record.createdAt)
      if (dataMap.has(dateKey)) {
        dataMap.get(dateKey).shopVisits += 1
      }
    }
  })
  
  return Array.from(dataMap.values()).sort()
}
```

### 3️⃣ **圖表渲染**
```javascript
initTrendChart() {
  const option = {
    title: {
      text: '店鋪詳情頁訪問趨勢 2025'
    },
    series: [{
      name: '店鋪詳情頁訪問',
      type: 'line',
      data: shopVisitsData,
      areaStyle: {
        color: {
          type: 'linear',
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      }
    }]
  }
  
  this.chart.setOption(option)
}
```

## 圖表特色

### 🎨 **視覺設計**
- **主色調**: 藍色系 (`#409EFF`)
- **圖表類型**: 帶面積填充的平滑折線圖
- **數據標籤**: 自動顯示數值，大於1000時顯示為 "1.2k" 格式
- **交互效果**: 懸停高亮、數據點提示

### 📊 **圖表配置**
```javascript
series: [{
  name: '店鋪詳情頁訪問',
  type: 'line',
  smooth: true,
  symbol: 'circle',
  symbolSize: 8,
  lineStyle: {
    width: 3,
    color: '#409EFF'
  },
  areaStyle: {
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
        { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
      ]
    }
  }
}]
```

## 導出功能

### 📁 **支持格式**
- **PNG圖片**: 高清圖片格式，適合報告使用
- **JPEG圖片**: 壓縮圖片格式，文件較小
- **SVG向量圖**: 可縮放向量格式，適合印刷
- **Excel文件**: 包含原始數據的電子表格

### 💾 **導出實現**
```javascript
exportXLSX() {
  const headers = ['日期', '店鋪詳情頁訪問次數']
  const ws_data = [
    headers,
    ...this.tableData.map(item => [
      item.date,
      item.shopVisits || 0
    ])
  ]
  
  const ws = XLSX.utils.aoa_to_sheet(ws_data)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '店鋪詳情頁訪問趨勢')
  
  XLSX.writeFile(wb, `店鋪詳情頁訪問趨勢_${this.getCurrentDateString()}.xlsx`)
}
```

## 時間限制

### 📅 **最早可選時間**
- **日期模式**: 2025年5月1日
- **月份模式**: 2025年5月

### 🚀 **快捷選項**
- 最近一週
- 最近一個月  
- 最近三個月
- 從5月1日開始（新增）

## 性能優勢

### ⚡ **查詢優化**
- **單次請求**: 避免多次API調用
- **本地處理**: 前端數據聚合，減少服務器負載
- **智能重試**: 自動處理503錯誤

### 📊 **數據效率**
- **精確篩選**: 只獲取 `food_detail` 事件數據
- **內存優化**: 高效的Map數據結構
- **實時處理**: 即時數據聚合和展示

## 使用場景

### 🏪 **店鋪運營分析**
- 監控店鋪詳情頁的訪問趨勢
- 分析用戶對店鋪信息的關注度
- 評估店鋪推廣活動的效果

### 📈 **業務決策支持**
- 識別店鋪訪問的高峰時段
- 分析季節性訪問模式
- 為店鋪優化提供數據支持

### 📊 **報告生成**
- 生成專業的訪問趨勢報告
- 導出數據用於進一步分析
- 與其他業務指標進行對比

## 技術特點

### 🔧 **基於成熟架構**
- 繼承 `device-trend.vue` 的優秀設計
- 使用相同的技術棧和組件庫
- 保持一致的用戶體驗

### 🛡️ **錯誤處理**
- 完善的異常捕獲機制
- 用戶友好的錯誤提示
- 自動重試和降級處理

### 📱 **響應式設計**
- 適配不同屏幕尺寸
- 移動端友好的交互設計
- 靈活的佈局調整

這個新的店鋪詳情頁訪問趨勢報告頁面為店鋪運營提供了專業、高效的數據分析工具，幫助業務團隊更好地理解用戶行為和優化店鋪策略。
