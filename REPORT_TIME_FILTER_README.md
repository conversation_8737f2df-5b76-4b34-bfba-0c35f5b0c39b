# Report页面时间筛选功能实现

## 功能概述

为Report页面添加了完整的时间筛选功能，支持年份、月份和日期范围三种筛选方式，完全兼容Strapi后台的筛选语法。

## 实现的功能

### 1. 筛选类型选择
- **年份筛选**: 选择特定年份，筛选该年度的所有数据
- **月份筛选**: 选择特定月份，筛选该月的所有数据  
- **日期范围筛选**: 选择开始和结束日期，筛选指定日期范围内的数据

### 2. Strapi筛选语法支持
根据不同的筛选类型，自动生成对应的Strapi筛选参数：

#### 年份筛选
```
filters[$and][0][createdAt][$gte]=2025-01-01T00:00:00.000Z
filters[$and][1][createdAt][$lt]=2026-01-01T00:00:00.000Z
```

#### 月份筛选
```
filters[$and][0][createdAt][$gte]=2025-05-01T00:00:00.000Z
filters[$and][1][createdAt][$lt]=2025-06-01T00:00:00.000Z
```

#### 日期范围筛选
```
filters[$and][0][createdAt][$gte]=2025-05-01T00:00:00.000Z
filters[$and][1][createdAt][$lte]=2025-05-31T23:59:59.999Z
```

### 3. 用户界面
- 直观的筛选类型选择下拉框
- 根据选择的筛选类型动态显示对应的日期选择器
- 搜索和重置按钮
- 关键词搜索输入框

## 技术实现

### 核心方法

1. **timeToTimestamp(time)**: 时间转换为ISO格式
2. **buildYearFilter(year)**: 构建年份筛选参数
3. **buildMonthFilter(monthStr)**: 构建月份筛选参数
4. **buildDateRangeFilter(dateRange)**: 构建日期范围筛选参数
5. **handleSearch()**: 执行搜索，根据筛选类型构建相应参数
6. **resetSearch()**: 重置所有筛选条件

### 数据结构

```javascript
data() {
  return {
    filterType: 'daterange', // 筛选类型：year, month, daterange
    queryYear: '',           // 年份筛选值
    queryMonth: '',          // 月份筛选值
    queryDateRange: [],      // 日期范围筛选值
    listQuery: {             // 列表请求参数
      page: 1,
      pageSize: 50,
      sort: 'createdAt:DESC',
      _q: ''
    }
  }
}
```

## 使用方法

1. 打开Report页面
2. 选择筛选类型（年份/月份/日期范围）
3. 根据选择的类型设置相应的时间值
4. 点击"搜索"按钮执行筛选
5. 点击"重置"按钮清除所有筛选条件

## 特性

- ✅ 支持三种时间筛选方式
- ✅ 完全兼容Strapi筛选语法
- ✅ 动态UI界面，根据筛选类型显示对应控件
- ✅ 自动时间格式转换
- ✅ 智能参数构建，避免参数冲突
- ✅ 完整的重置功能
- ✅ 响应式设计
- ✅ 中文界面

## 示例用法

### 筛选2025年的数据
1. 选择筛选类型为"年份"
2. 选择年份"2025"
3. 点击搜索

### 筛选2025年5月的数据
1. 选择筛选类型为"月份"
2. 选择月份"2025-05"
3. 点击搜索

### 筛选2025年5月1日到5月31日的数据
1. 选择筛选类型为"日期范围"
2. 设置开始日期为"2025-05-01"
3. 设置结束日期为"2025-05-31"
4. 点击搜索

## 注意事项

- 时间筛选基于数据的`createdAt`字段
- 所有时间都转换为UTC格式发送给后台
- 日期范围筛选包含开始日期的00:00:00和结束日期的23:59:59
- 年份和月份筛选使用`$gte`和`$lt`操作符，确保精确的时间范围
- 日期范围筛选使用`$gte`和`$lte`操作符，包含边界日期
