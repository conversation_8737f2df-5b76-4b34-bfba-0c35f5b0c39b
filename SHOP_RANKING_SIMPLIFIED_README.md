# 店鋪訪問排行榜頁面 - 簡化版

## 功能概述

根據用戶需求，對 `report-food.vue` 頁面進行了重大簡化和優化，實現以下核心功能：

1. **僅按月查詢**: 簡化時間選擇，只支持按月份查詢數據
2. **設備篩選**: 默認查看所有設備，支持篩選特定設備
3. **橫向滾動圖表**: 支持查看大量店鋪數據的完整展示

## 核心改進

### 🗓️ **1. 簡化時間選擇**

#### **移除複雜的時間維度**
```vue
<!-- ❌ 移除：複雜的時間維度選擇 -->
<el-select v-model="timeDimension">
  <el-option label="按天統計" value="day" />
  <el-option label="按月統計" value="month" />
</el-select>

<!-- ✅ 簡化：僅保留月份選擇 -->
<el-date-picker
  v-model="selectedMonth"
  type="month"
  placeholder="選擇月份"
  format="yyyy年MM月"
  value-format="yyyy-MM"
/>
```

#### **智能默認月份**
```javascript
setDefaultMonth() {
  const currentDate = new Date()
  const minDate = new Date('2025-05-01')
  
  // 如果當前日期早於2025年5月，則設置為2025年5月
  if (currentDate < minDate) {
    this.selectedMonth = '2025-05'
  } else {
    this.selectedMonth = currentDate.toISOString().substr(0, 7)
  }
}
```

### 🖥️ **2. 設備篩選功能**

#### **設備列表獲取**
```javascript
async getDevices() {
  try {
    const response = await fetch('https://yohomall.esc-map.com/api/machines')
    const data = await response.json()
    this.devices = data.data || []
    console.log('獲取到設備列表:', this.devices.length, '個設備')
  } catch (error) {
    console.error('獲取設備列表失敗:', error)
    this.devices = []
  }
}
```

#### **設備選擇界面**
```vue
<el-select 
  v-model="selectedDevice" 
  placeholder="選擇設備（默認全部）"
  clearable
  filterable
>
  <el-option label="全部設備" value="" />
  <el-option 
    v-for="device in devices" 
    :key="device.machineId" 
    :label="`${device.name} (${device.mall} ${device.floor})`"
    :value="device.machineId"
  />
</el-select>
```

#### **API查詢參數動態構建**
```javascript
const params = {
  page: 1,
  pageSize: 10000,
  'filters[$and][0][createdAt][$gte]': startDate,
  'filters[$and][1][createdAt][$lte]': endDate,
  'filters[$and][2][event][$eq]': 'food_detail'
}

// 如果選擇了特定設備，添加設備篩選
if (this.selectedDevice) {
  params['filters[$and][3][fid][$eq]'] = this.selectedDevice
}
```

### 📊 **3. 橫向滾動圖表**

#### **數據縮放配置**
```javascript
dataZoom: [{
  type: 'slider',
  show: true,
  xAxisIndex: [0],
  start: 0,
  end: topShops.length > 10 ? 50 : 100, // 如果店鋪數量超過10個，默認顯示50%
  bottom: '5%'
}, {
  type: 'inside',
  xAxisIndex: [0],
  start: 0,
  end: topShops.length > 10 ? 50 : 100
}]
```

#### **智能顯示策略**
- **≤10個店鋪**: 顯示全部，無需滾動
- **>10個店鋪**: 默認顯示50%，支持滾動查看全部
- **滾動方式**: 支持滑塊滾動和鼠標滾輪滾動

## 數據處理優化

### 🕐 **時間範圍計算**
```javascript
getMonthTimeRange() {
  if (!this.selectedMonth) {
    this.$message.warning('請選擇查詢月份')
    return null
  }

  const [year, month] = this.selectedMonth.split('-')
  const startDate = `${year}-${month}-01T00:00:00.000Z`
  
  // 計算下個月的第一天作為結束時間
  let nextMonth = parseInt(month) + 1
  let nextYear = parseInt(year)
  if (nextMonth > 12) {
    nextMonth = 1
    nextYear += 1
  }
  const endDate = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-01T00:00:00.000Z`

  return { startDate, endDate }
}
```

### 🔍 **設備篩選邏輯**
```javascript
// 動態構建查詢參數
const params = {
  // 基礎時間和事件篩選
  'filters[$and][0][createdAt][$gte]': startDate,
  'filters[$and][1][createdAt][$lte]': endDate,
  'filters[$and][2][event][$eq]': 'food_detail'
}

// 條件性添加設備篩選
if (this.selectedDevice) {
  params['filters[$and][3][fid][$eq]'] = this.selectedDevice
}
```

## 用戶界面優化

### 🎨 **簡潔的查詢界面**
```vue
<el-form :inline="true" class="demo-form-inline">
  <!-- 月份選擇 -->
  <el-form-item label="查詢月份">
    <el-date-picker v-model="selectedMonth" type="month" />
  </el-form-item>

  <!-- 設備選擇 -->
  <el-form-item label="選擇設備">
    <el-select v-model="selectedDevice" clearable filterable>
      <el-option label="全部設備" value="" />
      <!-- 設備選項 -->
    </el-select>
  </el-form-item>

  <!-- 操作按鈕 -->
  <el-form-item>
    <el-button type="primary" @click="generateTrendChart">
      生成店鋪訪問排行榜
    </el-button>
    <el-button @click="resetSearch">重置</el-button>
  </el-form-item>
</el-form>
```

### 💬 **智能提示信息**
```javascript
const deviceText = this.selectedDevice ? 
  `設備 ${this.getDeviceName(this.selectedDevice)} 的` : '所有設備的'

this.$message.info(`正在獲取 ${this.selectedMonth} ${deviceText}店鋪詳情頁訪問數據...`)
```

### 🏷️ **設備名稱顯示**
```javascript
getDeviceName(deviceId) {
  const device = this.devices.find(d => d.machineId === deviceId)
  return device ? `${device.name} (${device.mall} ${device.floor})` : `設備${deviceId}`
}
```

## 圖表增強功能

### 📈 **橫向滾動支持**
- **滑塊控制**: 底部滑塊可拖拽查看不同範圍
- **鼠標滾輪**: 在圖表區域使用滾輪橫向滾動
- **智能縮放**: 根據數據量自動調整顯示比例

### 🎯 **數據展示優化**
- **Top 20顯示**: 最多顯示前20名店鋪，避免圖表過於擁擠
- **動態標籤**: 店鋪名稱過長時自動截斷並添加省略號
- **旋轉標籤**: X軸標籤旋轉45度，避免重疊

### 🎨 **視覺效果**
- **漸變色柱**: 使用藍色漸變填充
- **圓角設計**: 柱子頂部圓角，更加美觀
- **懸停效果**: 鼠標懸停時顏色反轉

## 性能優化

### ⚡ **查詢效率**
- **單月查詢**: 相比多月查詢，大幅減少數據量
- **條件篩選**: 設備篩選在API層面進行，減少傳輸數據
- **一次性獲取**: 10000條記錄的大頁面，減少分頁請求

### 💾 **內存優化**
- **精簡數據結構**: 移除不必要的時間維度處理
- **智能分頁**: 根據實際數據量決定是否需要分頁
- **及時清理**: 重置時清理圖表實例和數據

## 使用場景

### 📊 **月度店鋪分析**
- 查看特定月份的店鋪訪問排行
- 識別最受歡迎的店鋪
- 分析店鋪訪問趨勢

### 🖥️ **設備特定分析**
- 查看特定設備上的店鋪訪問情況
- 對比不同設備的用戶偏好
- 設備效果評估

### 📈 **數據導出分析**
- 導出完整的店鋪排行數據
- 支持多種格式（PNG、JPEG、SVG、Excel）
- 便於進一步分析和報告

## 技術特點

### 🔧 **API集成**
- **設備API**: `https://yohomall.esc-map.com/api/machines`
- **統計API**: Strapi `statistical` 集合
- **動態篩選**: 支持時間、事件、設備的組合篩選

### 📱 **響應式設計**
- 適配不同屏幕尺寸
- 圖表自動調整大小
- 移動端友好的交互

### 🛡️ **錯誤處理**
- 網絡錯誤自動重試
- 用戶友好的錯誤提示
- 數據驗證和邊界處理

這次簡化大幅提升了頁面的易用性和性能，為用戶提供了更加專注和高效的店鋪訪問分析工具。
